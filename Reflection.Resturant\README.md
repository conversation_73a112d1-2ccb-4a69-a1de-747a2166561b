# Restaurant POS System

A modern React-based Point of Sale system for restaurants with secure two-step authentication.

This code was generated by [Magic Patterns](https://magicpatterns.com) for this design: [Source Design](https://www.magicpatterns.com/c/9ziczyjbtaxnvhcuqduchd)

## Features

- **Two-Step Authentication**: Secure login process with RSA encryption
- **Database Selection**: Support for multiple databases per user
- **Comprehensive API Integration**: Products, families, units, prices, and currencies
- **IndexedDB Caching**: Local data storage with stamp-based incremental updates
- **Smart Incremental Sync**: Only fetches changed data since last sync
- **Multi-unit Support**: Products with multiple units and pricing
- **Multi-currency Support**: Price display in different currencies
- **System Configuration**: Options API for system settings
- **Complete Order Management**: Checkout, invoice, and receipt generation
- **Table Management**: Dine-in orders linked to tables with auto-save
- **Auto-Sync**: Automatic data synchronization every 15 minutes
- **Order Tracking**: Real-time order status management
- **Offline Capability**: Works offline with cached data
- **Data Integrity**: Automatic filtering of deleted records
- **Modern UI**: Built with React, TypeScript, and Tailwind CSS
- **Responsive Design**: Works on desktop and mobile devices
- **Testing**: Comprehensive test suite with Jest

## Authentication Flow

The application implements a two-step authentication process:

### Step 1: Initial Login (LoginRSA)
- User enters username and password
- Password is encrypted using RSA public key
- API call to `/api/Login/LoginRSA` endpoint
- Returns user information and available databases

### Step 2: Token Generation (GenerateTokenRSA)
- If multiple databases are available, user selects one
- If only one database, it's selected automatically
- API call to `/api/Login/GenerateTokenRSA` endpoint
- Returns JWT token for authenticated session

### Logout
- API call to `/api/Role/Logout` endpoint with UserId and ApplicationId parameters
- Sets user as logged out in the database server-side
- Uses proper authentication headers (Bearer token and ClientKey)
- Clears user session data on successful logout
- Loading state with visual feedback during logout process
- Handles errors gracefully with user feedback

## Environment Variables

Create a `.env` file in the root directory with the following variables:

```env
REACT_APP_APPLICATION_ID=your-application-id-here
REACT_APP_IDENTITY_KEY=your-identity-key-here
```

## Getting Started

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```
3. Set up environment variables (see above)
4. Start the development server:
   ```bash
   npm run dev
   ```

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm test` - Run tests
- `npm run test:watch` - Run tests in watch mode
- `npm run lint` - Run ESLint

## API Configuration

The application integrates with two API services:

### Authentication API (`http://*************:2828`)
- **LoginRSA Endpoint**: `/api/Login/LoginRSA`
- **GenerateTokenRSA Endpoint**: `/api/Login/GenerateTokenRSA`
- **Logout Endpoint**: `/api/Role/Logout`

### Products API (`http://*************:8055`)
- **GetItemListAsync Endpoint**: `/api/Item/GetItemListAsync`
- **GetFamilyListAsync Endpoint**: `/api/Family/GetFamilyListAsync`
- **GetItemUnitListAsync Endpoint**: `/api/ItemUnit/GetItemUnitListAsync`
- **GetUnitPriceListAsync Endpoint**: `/api/UnitPriceList/GetUnitPriceListAsync`
- **GetCurrencyListAsync Endpoint**: `/api/currency/GetCurrencyListAsync`
- **GetOptionListAsync Endpoint**: `/api/Option/GetOptionListAsync`

## Data Management

### IndexedDB Integration
- **Local Storage**: All data cached locally using IndexedDB
- **Incremental Sync**: Only fetches data newer than local maximum stamp
- **No Data Loss**: Updates existing records instead of clearing all data
- **Deleted Record Filtering**: Automatically filters out deleted records
- **Offline Support**: Application works without internet connection
- **Performance**: Fast data access from local cache
- **Smart Updates**: Efficient synchronization reduces API calls

### Unified Database Structure (`ResturantPOSDB`)

**Single Database Architecture**: The entire application uses only one IndexedDB database (`ResturantPOSDB`) for all data storage, eliminating database fragmentation and simplifying data management.

#### **Product & Business Data**
- **Items**: Product information with pricing and categories
- **Families**: Product categories with hierarchical organization
- **ItemUnits**: Product units and barcode information
- **UnitPrices**: Pricing data for different units
- **Currencies**: Multi-currency support with proper formatting
- **Options**: System configuration settings

#### **Order Management**
- **Orders**: Complete order lifecycle with table linking
- **Invoices**: Detailed invoice generation with tax calculations
- **Receipts**: Customer receipt generation and storage

#### **Table & Layout Management**
- **DiningTables**: Operational restaurant tables with order linking
- **TableLayouts**: Multiple floor plan layouts with table positioning
- **Active Layout System**: Switch between different table arrangements

#### **System Data**
- **Metadata**: Sync status and auto-sync information

## Security

- Passwords are encrypted using RSA encryption before transmission
- JWT tokens are used for session management
- Environment variables are used for sensitive configuration
- Local data encrypted in IndexedDB

## Testing

Run the test suite:

```bash
npm test
```

Tests cover:
- Successful login scenarios
- Failed login scenarios
- Token generation
- Database selection logic
- Logout functionality
- Product API integration
- Family API integration
- Product filtering and searching by family
- Order checkout and management
- Invoice and receipt generation
- Order status updates
- Error handling for all operations
