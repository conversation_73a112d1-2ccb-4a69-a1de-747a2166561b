# Table Management & Auto-Sync Implementation

This document describes the implementation of table linking for dine-in orders and automatic data synchronization functionality.

## Table Management Features

### 1. **Table Linking for Dine-In Orders**

#### **Automatic Table Selection**
- When user selects "Dine-In" order type, table selector modal appears
- Shows available tables (green) and occupied tables (red) with existing orders
- User can select any table to start or continue an order

#### **Auto-Save Functionality**
- Orders are automatically saved to IndexedDB every 2 seconds when modified
- Linked to specific table for dine-in orders
- Preserves order state even if app is closed or refreshed

#### **Order Persistence**
- When user selects an occupied table, existing order automatically loads
- All items, customer info, and discounts are restored
- Seamless continuation of interrupted orders

### 2. **Table Service Architecture**

#### **Table Data Structure**
```typescript
interface Table {
  id: number;
  x: number;
  y: number;
  width: number;
  height: number;
  number: number;
  seats: number;
  status: 'available' | 'occupied' | 'reserved';
  borderColor: string;
  currentOrderId?: string; // Link to active order
  lastOrderTime?: number; // Last order timestamp
}
```

#### **Key Functions**
- `linkOrderToTable()`: Associates order with table
- `autoSaveOrderToTable()`: Saves draft order to table
- `loadOrderFromTable()`: Retrieves existing order from table
- `clearTable()`: Removes order association when completed

### 3. **Enhanced Order Flow**

#### **Dine-In Order Process**
```
1. User selects "Dine-In" order type
2. Table selector modal appears
3. User selects table (available or occupied)
4. If occupied: existing order loads automatically
5. User adds/modifies items
6. Order auto-saves every 2 seconds
7. During checkout: order linked to table
8. When completed: table status reset to available
```

#### **Order Status Integration**
- Table status automatically updates based on order status
- `pending/preparing/ready` → Table remains occupied
- `completed/cancelled` → Table becomes available

## Auto-Sync Service

### 1. **Automatic Data Synchronization**

#### **Configuration**
- **Sync Interval**: 15 minutes (configurable)
- **Auto-Start**: Enabled by default
- **Background Operation**: Runs independently of user actions

#### **Sync Process**
```
1. Service starts on app initialization
2. Performs initial sync immediately
3. Sets up recurring sync every 15 minutes
4. Uses existing incremental sync from itemService
5. Updates sync status and statistics
```

### 2. **Auto-Sync Status Tracking**

#### **Status Information**
```typescript
interface AutoSyncStatus {
  isEnabled: boolean;
  lastSyncTime: number;
  nextSyncTime: number;
  syncCount: number;
  lastError?: string;
}
```

#### **Monitoring Features**
- Track last sync time and next scheduled sync
- Count total sync operations
- Log and display sync errors
- Calculate time remaining until next sync

### 3. **Auto-Sync Service Functions**

#### **Core Operations**
- `startAutoSync()`: Initialize and start auto-sync service
- `stopAutoSync()`: Stop auto-sync service
- `triggerManualSync()`: Force immediate sync
- `getAutoSyncStatus()`: Get current sync status
- `getTimeUntilNextSync()`: Calculate remaining time

#### **Error Handling**
- Graceful handling of network failures
- Retry mechanism with exponential backoff
- Error logging and user notification
- Fallback to cached data on sync failure

## Implementation Details

### 1. **Database Schema Updates**

#### **New Object Stores**
```typescript
// Tables store
tables: {
  key: number;
  value: Table;
  indexes: { number: number; status: string };
}

// Enhanced orders with table linking
orders: {
  tableId?: number;
  tableName?: string;
  // ... existing properties
}
```

### 2. **POS Integration**

#### **Enhanced OrderSummary Component**
- Table selection button for dine-in orders
- Visual indicator of selected table
- Change table option for flexibility

#### **Auto-Save Implementation**
```typescript
// Auto-save effect with debouncing
useEffect(() => {
  const autoSave = async () => {
    if (orderType === 'dine-in' && selectedTableId && currentOrder.length > 0) {
      await autoSaveOrderToTable(selectedTableId, currentOrder, customerInfo);
    }
  };
  
  const timeoutId = setTimeout(autoSave, 2000);
  return () => clearTimeout(timeoutId);
}, [currentOrder, customerInfo, orderType, selectedTableId]);
```

### 3. **Table Selector Modal**

#### **Features**
- Grid layout showing all tables
- Color-coded status indicators
- Available tables (green) vs occupied tables (red)
- Table information (number, seats, status)
- Click to select functionality

#### **User Experience**
- Clear visual distinction between table states
- Ability to select occupied tables to continue orders
- Cancel option to return without selection
- Responsive design for different screen sizes

## Benefits

### 1. **For Restaurant Staff**
- **Seamless Service**: Orders persist across app sessions
- **Table Management**: Clear visibility of table status
- **Order Continuity**: Resume interrupted orders instantly
- **Reduced Errors**: Automatic order-table association

### 2. **For System Reliability**
- **Data Persistence**: Orders never lost due to app crashes
- **Automatic Sync**: Always up-to-date product data
- **Offline Capability**: Full functionality without internet
- **Error Recovery**: Graceful handling of sync failures

### 3. **For Business Operations**
- **Improved Efficiency**: Faster order processing
- **Better Tracking**: Complete order-table audit trail
- **Reduced Downtime**: Automatic data updates
- **Enhanced Reporting**: Table utilization metrics

## Configuration Options

### 1. **Auto-Sync Settings**
```typescript
// Configurable sync interval
const AUTO_SYNC_INTERVAL = 15 * 60 * 1000; // 15 minutes

// Enable/disable auto-sync
await updateAutoSyncSettings({
  enabled: true,
  intervalMinutes: 15
});
```

### 2. **Table Management Settings**
```typescript
// Auto-save debounce time
const AUTO_SAVE_DELAY = 2000; // 2 seconds

// Enable/disable auto-save
setAutoSaveEnabled(true);
```

## Error Handling

### 1. **Auto-Sync Errors**
- Network connectivity issues
- API server unavailability
- Authentication token expiration
- Data corruption or conflicts

### 2. **Table Management Errors**
- Table not found
- Order loading failures
- Auto-save conflicts
- Database access issues

### 3. **Recovery Mechanisms**
- Automatic retry with exponential backoff
- Fallback to cached data
- User notification of sync status
- Manual sync trigger option

## Testing

### 1. **Auto-Sync Testing**
- Service initialization and cleanup
- Sync interval timing
- Error handling and recovery
- Status tracking accuracy

### 2. **Table Management Testing**
- Order-table linking
- Auto-save functionality
- Order loading from tables
- Table status updates

### 3. **Integration Testing**
- End-to-end order flow
- Cross-session order persistence
- Multi-table order management
- Sync service integration

This implementation provides a robust, user-friendly table management system with automatic data synchronization, ensuring reliable restaurant operations with minimal manual intervention.
