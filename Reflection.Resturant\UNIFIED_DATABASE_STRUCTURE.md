# Unified Database Structure Implementation

This document describes the consolidation of IndexedDB databases and the resolution of table naming conflicts in the Restaurant POS system.

## Database Consolidation

### **Before: Multiple Databases**
- `ResturantPOSDB`: Main application data (products, orders, invoices, etc.)
- `tableLayoutManager`: Table layout management (separate database)

### **After: Single Unified Database**
- `ResturantPOSDB`: All application data including layouts and tables

## Resolved Naming Conflicts

### **Problem**
- `ResturantPOSDB` had a `tables` store for operational dining tables
- `tableLayoutManager` had `layouts` containing `tables` for floor plan management
- Naming conflict between operational tables and layout tables

### **Solution: Clear Type Separation**

#### **DiningTable** (Operational Tables)
```typescript
interface DiningTable {
  id: number;
  number: number;
  seats: number;
  status: 'available' | 'occupied' | 'reserved';
  currentOrderId?: string; // Link to active order
  lastOrderTime?: number; // Last order timestamp
  layoutId: string; // Reference to which layout this table belongs to
}
```

#### **LayoutTable** (Floor Plan Tables)
```typescript
interface LayoutTable {
  id: number;
  x: number;
  y: number;
  width: number;
  height: number;
  number: number;
  seats: number;
  status: 'available' | 'occupied' | 'reserved';
  borderColor: string;
}
```

#### **TableLayout** (Floor Plan Layouts)
```typescript
interface TableLayout {
  id: string;
  name: string;
  tables: LayoutTable[];
  floorPlanImage: string | null;
  isActive: boolean; // Which layout is currently active
  createdAt: string;
  updatedAt: string;
}
```

## Unified Database Schema

### **Object Stores in ResturantPOSDB**

#### **Existing Stores**
- `items`: Product data
- `families`: Product categories
- `itemUnits`: Product units/barcodes
- `unitPrices`: Pricing data
- `currencies`: Currency information
- `options`: System configuration
- `orders`: Order management
- `invoices`: Invoice data
- `receipts`: Receipt data

#### **New Stores**
- `diningTables`: Operational dining tables
- `tableLayouts`: Floor plan layouts

#### **Store Definitions**
```typescript
diningTables: {
  key: number;
  value: DiningTable;
  indexes: { 
    number: number; 
    status: string; 
    layoutId: string 
  };
}

tableLayouts: {
  key: string;
  value: TableLayout;
  indexes: { 
    name: string; 
    isActive: boolean; 
    createdAt: string 
  };
}
```

## Service Architecture

### **Table Service** (`tableService.ts`)
Handles both dining tables and layout management:

#### **Dining Table Operations**
- `getAllDiningTables()`: Get all operational tables
- `getActiveDiningTables()`: Get tables for active layout
- `updateDiningTableStatus()`: Update table status
- `linkOrderToTable()`: Associate orders with tables
- `clearTable()`: Reset table when order completes

#### **Layout Management**
- `getAllTableLayouts()`: Get all floor plan layouts
- `getActiveLayout()`: Get currently active layout
- `setActiveLayout()`: Switch active layout
- `generateDiningTablesFromLayout()`: Create operational tables from layout

### **Layout Service** (`layoutService.ts`)
Dedicated layout management service:

#### **Core Functions**
- `createLayout()`: Create new floor plan layout
- `updateLayout()`: Modify existing layout
- `deleteLayout()`: Remove layout (if not active)
- `duplicateLayout()`: Copy existing layout
- `validateLayout()`: Ensure layout integrity

#### **Import/Export**
- `exportLayout()`: Export layout as JSON
- `importLayout()`: Import layout from JSON
- `initializeDefaultTables()`: Create default layout if none exists

## Data Flow & Relationships

### **Layout to Dining Table Flow**
```
1. User creates/selects TableLayout
2. setActiveLayout() called
3. generateDiningTablesFromLayout() creates DiningTables
4. DiningTables linked to layout via layoutId
5. Orders can be linked to DiningTables
```

### **Multi-Layout Support**
- Multiple layouts can exist simultaneously
- Only one layout is active at a time
- Switching layouts regenerates dining tables
- Each dining table knows its parent layout

## Migration Strategy

### **Automatic Migration**
- Old `tableLayoutManager` database remains untouched
- New unified structure initializes with defaults
- Users can manually import old layouts if needed

### **Data Preservation**
- Existing orders and operational data preserved
- Layout data can be migrated manually
- No data loss during transition

## Benefits

### **1. Simplified Architecture**
- Single database for all data
- Consistent backup/restore procedures
- Unified data management

### **2. Clear Separation of Concerns**
- **DiningTables**: Operational restaurant tables
- **LayoutTables**: Floor plan design elements
- **TableLayouts**: Complete floor plan configurations

### **3. Enhanced Functionality**
- Multiple layout support
- Easy layout switching
- Layout import/export capabilities
- Better table-order relationships

### **4. Improved Performance**
- Single database connection
- Optimized queries with proper indexes
- Reduced memory footprint

## Usage Examples

### **Creating a New Layout**
```typescript
const newLayout = await createLayout(
  'Evening Layout',
  [
    { id: 1, x: 100, y: 100, width: 80, height: 80, number: 1, seats: 4, status: 'available', borderColor: '#16a34a' },
    { id: 2, x: 200, y: 100, width: 80, height: 80, number: 2, seats: 4, status: 'available', borderColor: '#16a34a' }
  ],
  null
);
```

### **Switching Active Layout**
```typescript
await setActiveLayout('evening-layout-id');
// This automatically generates new dining tables
```

### **Linking Order to Table**
```typescript
await linkOrderToTable('order-123', 5); // Table number 5
// Updates both order and dining table
```

### **Getting Active Tables for POS**
```typescript
const activeTables = await getActiveDiningTables();
// Returns only tables from currently active layout
```

## Testing & Validation

### **All 38 Tests Passing**
- Existing functionality preserved
- New services properly tested
- Database migration validated
- Type safety maintained

### **Build Verification**
- TypeScript compilation successful
- No runtime errors
- Proper type checking
- Clean build output

## Future Enhancements

### **Potential Improvements**
- Layout versioning system
- Table reservation scheduling
- Advanced layout templates
- Real-time layout synchronization
- Layout analytics and reporting

This unified database structure provides a solid foundation for scalable restaurant management with clear separation between operational and design concerns while maintaining full backward compatibility.
