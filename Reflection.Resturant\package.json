{"name": "magic-patterns-vite-template", "version": "0.0.1", "private": true, "type": "module", "scripts": {"dev": "npx vite", "build": "npx vite build", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "preview": "npx vite preview", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"devextreme": "^24.2.7", "devextreme-react": "^24.2.7", "jsencrypt": "^3.3.2", "lucide-react": "^0.441.0", "react": "^18.3.1", "react-dom": "^18.3.1"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/node": "^20.11.18", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@types/react-router-dom": "^5.3.3", "@typescript-eslint/eslint-plugin": "^5.54.0", "@typescript-eslint/parser": "^5.54.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "latest", "eslint": "^8.50.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.1", "jest": "^30.0.2", "jest-environment-jsdom": "^30.0.2", "postcss": "latest", "prettier": "^3.5.3", "react-router-dom": "^7.6.2", "tailwindcss": "3.4.17", "ts-jest": "^29.4.0", "typescript": "^5.5.4", "vite": "^5.2.0", "vite-plugin-pwa": "^1.0.0"}}