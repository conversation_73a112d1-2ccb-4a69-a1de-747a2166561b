import { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { InvoicePage } from './pages/InvoicePage';
import { PosPage } from './pages/PosPage';
import { OrderManagementPage } from './pages/OrderManagementPage';
import { CurrencyProvider } from './contexts/CurrencyContext';
import { PermissionsProvider } from './contexts/PermissionsContext';
import { LoginPage } from './pages/LoginPage';
import { ProfilePage } from './pages/ProfilePage';
import { Header } from './components/Header';
import { SessionDebugInfo } from './components/SessionDebugInfo';
import { ProtectedRoute } from './components/ProtectedRoute';
import { RoleDebugInfo } from './components/RoleDebugInfo';
// import { Footer } from './components/Footer';
import TableOverlayManager from './pages/TableOverlayManagerPage';
import LayoutViewerTabPage from './pages/LayoutViewerTabsPage';
import { restoreUserSession, getCurrentSessionInfo, extendUserSession } from './services/persistentAuthService';
import './App.css';
import config from 'devextreme/core/config';
import { licenseKey } from './devextreme-license';
config({
  licenseKey: licenseKey
});
export function App() {
  const [user, setUser] = useState<{ username: string; role: string } | null>(null);
  const [isCheckingSession, setIsCheckingSession] = useState(true);

  // Check for existing session on app startup
  useEffect(() => {
    const checkExistingSession = async () => {
      try {
        console.log('🔍 Checking for existing user session...');
        const sessionRestored = await restoreUserSession();

        if (sessionRestored) {
          // Get the actual session info to display correct username
          const sessionInfo = await getCurrentSessionInfo();
          console.log('✅ Session restored, user auto-logged in');
          setUser({
            username: sessionInfo?.username || 'Restored User',
            role: 'user'
          });
        } else {
          console.log('📭 No valid session found');
        }
      } catch (error) {
        console.error('❌ Error checking session:', error);
      } finally {
        setIsCheckingSession(false);
      }
    };

    checkExistingSession();
  }, []);

  // Extend session periodically when user is active
  useEffect(() => {
    if (!user) return;

    const extendSession = async () => {
      try {
        await extendUserSession();
      } catch (error) {
        console.error('Failed to extend session:', error);
      }
    };

    // Extend session every 30 minutes
    const interval = setInterval(extendSession, 30 * 60 * 1000);

    return () => clearInterval(interval);
  }, [user]);

  const handleLogin = (user: { username: string; role: string }) => {
    setUser(user);
  };

  const handleLogout = () => {
    setUser(null);
  };

  // Show loading screen while checking for existing session
  if (isCheckingSession) {
    return (
      <div className="flex items-center justify-center h-screen bg-gray-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Checking authentication...</p>
        </div>
      </div>
    );
  }

  return (
    <CurrencyProvider>
      <PermissionsProvider user={user}>
        <Router>
          <div className="flex flex-col h-screen bg-gray-100">
            <Header user={user} onLogout={handleLogout} />
            {/* Main Content */}
            <main className="flex-1 overflow-y-auto">
              <Routes>
                <Route path="/login" element={user ? <Navigate to="/" /> :  <LoginPage onLogin={handleLogin} />} />
                <Route
                  path="/"
                  element={
                    user ? (
                      <ProtectedRoute requiredPermission="pos" fallbackPath="/dashboard">
                        <PosPage />
                      </ProtectedRoute>
                    ) : (
                      <Navigate to="/login" />
                    )
                  }
                />
                <Route
                  path="/dashboard"
                  element={
                    user ? (
                      <div className="flex items-center justify-center h-64">
                        <div className="text-center">
                          <h2 className="text-2xl font-bold text-gray-800 mb-4">Welcome to Restaurant POS</h2>
                          <p className="text-gray-600">Please use the navigation menu to access available features.</p>
                        </div>
                      </div>
                    ) : (
                      <Navigate to="/login" />
                    )
                  }
                />
                <Route
                  path="/orders"
                  element={
                    user ? (
                      <ProtectedRoute requiredPermission="kitchen">
                        <OrderManagementPage />
                      </ProtectedRoute>
                    ) : (
                      <Navigate to="/login" />
                    )
                  }
                />
                {/* <Route path="/profile" element={user ? <ProfilePage user={user} /> : <Navigate to="/login" />} /> */}
                <Route
                  path="/table-manager"
                  element={
                    user ? (
                      <ProtectedRoute requiredPermission="tableManagement">
                        <TableOverlayManager />
                      </ProtectedRoute>
                    ) : (
                      <Navigate to="/login" />
                    )
                  }
                />
                <Route
                  path="/table-viewer"
                  element={
                    user ? (
                      <ProtectedRoute requiredPermission="tableManagement">
                        <LayoutViewerTabPage />
                      </ProtectedRoute>
                    ) : (
                      <Navigate to="/login" />
                    )
                  }
                />
                <Route
                  path="/invoices"
                  element={
                    user ? (
                      <ProtectedRoute requiredPermission="invoices">
                        <InvoicePage />
                      </ProtectedRoute>
                    ) : (
                      <Navigate to="/login" />
                    )
                  }
                />
              </Routes>
            </main>
            {/* <Footer /> */}

            {/* Debug component for testing persistent login */}
            {/* <SessionDebugInfo /> */}

            {/* Debug component for testing role-based access control */}
            {user && <RoleDebugInfo />}
          </div>
        </Router>
      </PermissionsProvider>
    </CurrencyProvider>
  );
}