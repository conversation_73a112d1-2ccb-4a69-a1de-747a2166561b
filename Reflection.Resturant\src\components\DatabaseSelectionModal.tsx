import React from 'react';
import { Database } from '../services/authService';

interface DatabaseSelectionModalProps {
  isOpen: boolean;
  databases: Database[];
  onSelect: (database: Database) => void;
  onClose: () => void;
}

export function DatabaseSelectionModal({ 
  isOpen, 
  databases, 
  onSelect, 
  onClose 
}: DatabaseSelectionModalProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-96 max-h-96 overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold">Select Database</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 text-xl"
          >
            ×
          </button>
        </div>
        
        <div className="space-y-2">
          {databases.map((database) => (
            <button
              key={database.id}
              onClick={() => onSelect(database)}
              className="w-full text-left p-3 border rounded hover:bg-gray-50 hover:border-blue-500 transition-colors"
            >
              <div className="font-medium">{database.name}</div>
              <div className="text-sm text-gray-600">{database.source}</div>
            </button>
          ))}
        </div>
        
        {databases.length === 0 && (
          <div className="text-center text-gray-500 py-4">
            No databases available
          </div>
        )}
      </div>
    </div>
  );
}
