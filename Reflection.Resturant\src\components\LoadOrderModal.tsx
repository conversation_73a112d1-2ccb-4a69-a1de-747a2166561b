import React, { useState, useEffect } from 'react';
import { X, Clock, User, MapPin, Home, ShoppingBag } from 'lucide-react';
import { DiningTable, Order, OrderType } from '../types';
import { getIncompleteOrders, getIncompleteOrdersByType } from '../services/orderService';
import { formatPrice } from '../services/itemService';
import { useCurrency } from '../contexts/CurrencyContext';
import { getActiveDiningTables } from '../services/tableService';
interface LoadOrderModalProps {
  isOpen: boolean;
  onClose: () => void;
  onLoadOrder: (order: Order) => void;
  currentOrderType?: OrderType;
}

export function LoadOrderModal({ isOpen, onClose, onLoadOrder, currentOrderType }: LoadOrderModalProps) {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [selectedOrderType, setSelectedOrderType] = useState<OrderType | 'all'>('all');
  const { defaultCurrency } = useCurrency();
  const [diningTables, setDiningTables] = useState<DiningTable[]>([]);

  useEffect(() => {
    if (isOpen) {
      loadOrders();
    }
  }, [isOpen, selectedOrderType]);

  const loadOrders = async () => {
    try {
      setLoading(true);
      let loadedOrders: Order[];

      if (selectedOrderType === 'all') {
        loadedOrders = await getIncompleteOrders();
      } else {
        loadedOrders = await getIncompleteOrdersByType(selectedOrderType);
      }
      let dinTables = await getActiveDiningTables();
      setDiningTables(dinTables);

      setOrders(loadedOrders);
    } catch (error) {
      console.error('Error loading orders:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleLoadOrder = (order: Order) => {
    onLoadOrder(order);
    onClose();
  };

  const getOrderTypeIcon = (orderType: OrderType) => {
    switch (orderType) {
      case 'dine-in':
        return <Home size={16} className="text-blue-600" />;
      case 'takeout':
        return <ShoppingBag size={16} className="text-green-600" />;
      case 'delivery':
        return <MapPin size={16} className="text-purple-600" />;
      default:
        return null;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'preparing':
        return 'bg-blue-100 text-blue-800';
      case 'ready':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDateTime = (timestamp: number) => {
    const date = new Date(timestamp);
    return {
      date: date.getFullYear().toString() + "-" +
        (date.getMonth() + 1).toString().padStart(2, '0') + "-" +
        date.getDate().toString().padStart(2, '0'),
      time: date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    };
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[80vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-bold text-gray-800">Load Saved Order</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <X size={20} />
          </button>
        </div>

        {/* Filter */}
        <div className="p-4 border-b bg-gray-50">
          <div className="flex items-center space-x-4">
            <span className="text-sm font-medium text-gray-700">Filter by type:</span>
            <div className="flex space-x-2">
              {(['all', 'dine-in', 'takeout', 'delivery'] as const).map((type) => (
                <button
                  key={type}
                  onClick={() => setSelectedOrderType(type)}
                  className={`px-3 py-1 rounded-full text-sm font-medium transition-colors flex items-center space-x-1 ${selectedOrderType === type
                    ? 'bg-blue-500 text-white'
                    : 'bg-white text-gray-600 hover:bg-gray-100'
                    }`}
                >
                  {type === 'all' ? '' : getOrderTypeIcon(type)}
                  <span>
                    {type === 'all' ? 'All Orders' : type.charAt(0).toUpperCase() + type.slice(1)}
                  </span>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Orders List */}
        <div className="flex-1 overflow-y-auto p-4" style={{ maxHeight: '400px' }}>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-2 text-gray-600">Loading orders...</span>
            </div>
          ) : orders.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Clock size={48} className="mx-auto mb-4 text-gray-300" />
              <p>No saved orders found</p>
              <p className="text-sm">Create and save an order to see it here</p>
            </div>
          ) : (
            <div className="grid gap-4">
              {orders.map((order) => {
                const { date, time } = formatDateTime(order.updatedAt);
                return (
                  <div
                    key={order.id}
                    className="border rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
                    onClick={() => handleLoadOrder(order)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          {getOrderTypeIcon(order.orderType)}
                          <span className="font-semibold text-gray-800">{order.orderNumber}</span>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                            {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                          </span>
                        </div>

                        <div className="text-sm text-gray-600 mb-2">
                          <div className="flex items-center space-x-4">
                            <span className="flex items-center">
                              <Clock size={14} className="mr-1" />
                              {date} at {time}
                            </span>
                            {order.customerInfo.name && (
                              <span className="flex items-center">
                                <User size={14} className="mr-1" />
                                {order.customerInfo.name}
                              </span>
                            )}
                            {order.tableName && (
                              <span className="text-blue-600 font-medium">
                                {diningTables.find(dt => dt.currentOrderId === order.id)?.name}
                              </span>
                            )}
                          </div>
                        </div>

                        <div className="text-sm text-gray-600">
                          {order.items.length} item{order.items.length !== 1 ? 's' : ''} •
                          <span className="font-semibold text-gray-800 ml-1">
                            {formatPrice(order.total, defaultCurrency || undefined)}
                          </span>
                        </div>
                      </div>

                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleLoadOrder(order);
                        }}
                        className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-sm font-medium"
                      >
                        Load Order
                      </button>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-4 border-t bg-gray-50">
          <div className="flex justify-end">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
