import { useState } from 'react';
import { Product,  Modifier } from '../types';
import { formatPrice , EnrichedProduct} from '../services/itemService';

export function ModifierModal({
  product,
  onClose,
  onAddToOrder
}: {
  product: EnrichedProduct;
  onClose: () => void;
  onAddToOrder: (product: Product, selectedModifiers: (Modifier & { quantity: number })[]) => void;
}) {
  const [selectedModifiers, setSelectedModifiers] = useState<(Modifier & { quantity: number })[]>([]);

  const handleModifierChange = (modifier: Modifier, quantity: number) => {
    if (quantity > 0) {
      const existingModifier = selectedModifiers.find(mod => mod.id === modifier.id);
      if (existingModifier) {
        setSelectedModifiers(selectedModifiers.map(mod => mod.id === modifier.id ? { ...mod, quantity } : mod));
      } else {
        setSelectedModifiers([...selectedModifiers, { ...modifier, quantity }]);
      }
    } else {
      setSelectedModifiers(selectedModifiers.filter(mod => mod.id !== modifier.id));
    }
  };

  const calculateTotalPrice = () => {
    const modifierTotal = selectedModifiers.reduce((total, mod) => total + (mod.price * mod.quantity) * (1 - mod.discount / 100), 0);
    return formatPrice((product.defaultPrice || 0) + modifierTotal, product.defaultCurrency);
  };

  return <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-full max-w-md p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold">{product.name}</h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            ✕
          </button>
        </div>
        <div className="mb-4">
          <p className="text-gray-600 mb-2">
            Base Price: {formatPrice(product.defaultPrice || 0, product.defaultCurrency)}
          </p>
          <h3 className="font-semibold mb-2">Select Options:</h3>
          <div className="space-y-2 max-h-60 overflow-y-auto">
            {product.modifiers && product.modifiers.map(modifier => <div key={modifier.id} className="flex items-center justify-between border rounded p-3">
                  <div>
                    <p className="font-medium">{modifier.name}</p>
                    <p className="text-sm text-gray-500">
                      + {formatPrice(modifier.price, product.defaultCurrency)}
                    </p>
                  </div>
                  <div className="flex items-center">
                    <input
                      type="number"
                      min="0"
                      className="w-16 text-center border rounded"
                      value={selectedModifiers.find(mod => mod.id === modifier.id)?.quantity || 0}
                      onChange={(e) => handleModifierChange(modifier, parseInt(e.target.value))}
                    />
                  </div>
                </div>)}
          </div>
        </div>
        <div className="flex justify-between items-center border-t pt-4">
          <p className="font-bold text-lg">Total: {calculateTotalPrice()}</p>
          <button onClick={() => onAddToOrder(product, selectedModifiers)} className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
            Add to Order
          </button>
        </div>
      </div>
    </div>;
}