import React, { useState, useEffect } from 'react';
import { X, RefreshCw, ArrowRightCircle } from 'lucide-react';
import { TableLayout, LayoutTable, DiningTable } from '../types';
import { getAllLayouts, getActiveLayout, getActiveLayouts } from '../services/layoutService';
import { getActiveDiningTables, moveOrderBetweenTables } from '../services/tableService';

interface MoveOrderModalProps {
  isOpen: boolean;
  currentTableId: string;
  onClose: () => void;
  onMoveComplete: () => void;
}

interface TableWithStatus extends LayoutTable {
  diningTableId: string;
  realStatus: DiningTable['status'];
  hasOrder: boolean;
  currentOrderId?: string;
  tableName: string;
}

interface LayoutViewerProps {
  layout: TableLayout | null;
  tablesWithStatus: TableWithStatus[];
  currentTableId: string;
  onTableClick: (table: TableWithStatus) => void;
}

const LayoutViewer: React.FC<LayoutViewerProps> = ({ layout, tablesWithStatus, currentTableId, onTableClick }) => {
  if (!layout) {
    return (
      <div className="flex-1 flex items-center justify-center text-gray-500">
        <div className="text-center">
          <ArrowRightCircle className="w-16 h-16 mx-auto mb-4 opacity-50" />
          <p className="text-xl">No layout available</p>
          <p className="text-sm">Please create a layout first</p>
        </div>
      </div>
    );
  }

  const getTableStatusColor = (table: TableWithStatus) => {
    if (table.diningTableId === currentTableId) return '#3b82f6'; // blue for current table
    switch (table.realStatus) {
      case 'available': return '#16a34a'; // green
      case 'occupied': return '#dc2626'; // red
      default: return table.borderColor;
    }
  };

  const getTableStatusBg = (table: TableWithStatus) => {
    if (table.diningTableId === currentTableId) return 'bg-blue-50 hover:bg-blue-100';
    switch (table.realStatus) {
      case 'available': return 'bg-green-50 hover:bg-green-100';
      case 'occupied': return 'bg-red-50 hover:bg-red-100';
      default: return 'bg-white hover:bg-gray-50';
    }
  };

  const getTableStatusText = (table: TableWithStatus) => {
    if (table.diningTableId === currentTableId) return '🔄 Current Table';
    if (table.hasOrder) return '📋 Has Order';
    switch (table.realStatus) {
      case 'occupied': return '🔒 Occupied';
      case 'available': return '✅ Available';
      default: return '';
    }
  };

  return (
    <div className="flex-1 relative overflow-auto bg-gray-50"
      style={{
        height: '600px',
        width: '800px',
        overflow: 'auto',
        position: 'relative'
      }}>
      <div
        className="min-w-full min-h-full relative"
        style={{
          backgroundImage: layout.floorPlanImage ? `url(${layout.floorPlanImage})` : 'none',
          backgroundSize: 'contain',
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'left top',
          minHeight: '800px',
          minWidth: '1200px',
        }}
      >
        {/* Grid overlay when no image */}
        {!layout.floorPlanImage && (
          <div
            className="absolute inset-0 opacity-5"
            style={{
              backgroundImage: `
                linear-gradient(rgba(0,0,0,0.3) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0,0,0,0.3) 1px, transparent 1px)
              `,
              backgroundSize: '20px 20px'
            }}
          />
        )}

        {/* Tables */}
        {tablesWithStatus.map(table => {
          const isCurrentTable = table.diningTableId === currentTableId;
          const isSelectable = table.realStatus === 'available' && !isCurrentTable;
          
          return (
            <div
              key={table.id}
              className={`absolute select-none z-10 hover:z-20 transition-all hover:scale-105 group ${
                isSelectable ? 'cursor-pointer' : 'cursor-not-allowed'
              }`}
              style={{
                left: table.x,
                top: table.y,
                width: table.width,
                height: table.height,
              }}
              onClick={() => isSelectable && onTableClick(table)}
              title={`Table ${table.number} - ${isCurrentTable ? 'Current Table' : table.realStatus}`}
            >
              {/* Table */}
              <div
                className={`w-full h-full rounded-lg border-4 flex items-center justify-center
                         font-bold text-lg shadow-lg bg-opacity-90 backdrop-blur-sm bg-transparent
                         hover:shadow-xl hover:bg-opacity-95 hover:border-opacity-80 transition-all
                         group-hover:scale-105 ${getTableStatusBg(table)}
                         ${isCurrentTable ? 'ring-4 ring-blue-400 ring-opacity-60' : ''}
                         ${table.realStatus === 'occupied' ? 'ring-4 ring-red-400 ring-opacity-60' : ''}
                         ${!isSelectable && !isCurrentTable ? 'opacity-50' : ''}
                         `
                        }
                style={{
                  borderColor: getTableStatusColor(table),
                  color: getTableStatusColor(table),
                  borderWidth: isCurrentTable ? '6px' : '4px'
                }}>
                <div className="text-center">
                  <div className="text-xl font-bold">{table.number}</div>
                  <div className="text-xs opacity-80">{table.seats} seats</div>
                  <div className="text-xs mt-1 px-2 py-1 bg-white bg-opacity-80 rounded font-medium">
                    {getTableStatusText(table)}
                  </div>
                </div>
              </div>

              {/* Status indicator */}
              <div
                className={`absolute -top-3 -left-3 w-6 h-6 rounded-full border-3 border-white shadow-lg
                         ${isCurrentTable ? 'animate-pulse' : ''}
                         ${table.hasOrder && !isCurrentTable ? 'animate-bounce' : ''}`}
                style={{
                  backgroundColor: getTableStatusColor(table),
                  borderWidth: '3px'
                }}
              >
                {isCurrentTable && (
                  <div className="absolute inset-0 flex items-center justify-center text-white text-xs font-bold">
                    🔄
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export function MoveOrderModal({ isOpen, currentTableId, onClose, onMoveComplete }: MoveOrderModalProps) {
  const [layouts, setLayouts] = useState<TableLayout[]>([]);
  const [activeLayouts, setActiveLayouts] = useState<TableLayout[]>([]);
  const [activeTab, setActiveTab] = useState<string | null>(null);
  const [tablesWithStatus, setTablesWithStatus] = useState<TableWithStatus[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isMoving, setIsMoving] = useState(false);
  const [selectedDestinationId, setSelectedDestinationId] = useState<string | null>(null);

  useEffect(() => {
    if (isOpen) {
      loadData();
    }
  }, [isOpen]);

  useEffect(() => {
    if (activeTab) {
      loadTablesForLayout(activeTab);
    }
  }, [activeTab, currentTableId]);

  const loadData = async () => {
    setIsLoading(true);
    setError(null);
    setSelectedDestinationId(null);

    try {
      // Load all layouts
      const savedLayouts = await getAllLayouts();
      const activeLayoutsList = await getActiveLayouts();
      
      const sortedLayouts = savedLayouts.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
      setLayouts(sortedLayouts);
      setActiveLayouts(activeLayoutsList);

      // Set first active layout as default, or first layout if no active layouts
      const defaultTab = activeLayoutsList.length > 0 ? activeLayoutsList[0].id : (sortedLayouts.length > 0 ? sortedLayouts[0].id : null);
      if (defaultTab) {
        setActiveTab(defaultTab.toString());
      } else {
        console.warn('No layouts available to set as default tab');
      }
    } catch (err) {
      console.error('Failed to load layouts:', err);
      setError(`Failed to load layouts: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setIsLoading(false);
    }
  };

  const loadTablesForLayout = async (layoutId: string) => {
    try {
      const layout = layouts.find(l => l.id.toString() === layoutId);
      if (!layout) {
        console.warn('Layout not found:', layoutId);
        setTablesWithStatus([]);
        return;
      }

      const diningTables = await getActiveDiningTables();

      // Map layout tables to dining tables and get their status
      const tablesWithStatusData = layout.tables.map((layoutTable) => {
        // Find corresponding dining table using composite ID
        const compositeId = `${layout.id}-${layoutTable.id}`;
        const diningTable = diningTables.find(dt => dt.id === compositeId);

        if (diningTable) {
          const hasOrder = !!diningTable.currentOrderId;
          return {
            ...layoutTable,
            diningTableId: diningTable.id,
            realStatus: diningTable.status,
            hasOrder,
            currentOrderId: diningTable.currentOrderId,
            tableName: `${layout.name} - ${layoutTable.number}`
          } as TableWithStatus;
        } else {
          // If no dining table found, create a virtual one
          return {
            ...layoutTable,
            diningTableId: compositeId,
            realStatus: 'available' as const,
            hasOrder: false,
            tableName: `${layout.name} - ${layoutTable.number}`
          } as TableWithStatus;
        }
      });

      setTablesWithStatus(tablesWithStatusData);
    } catch (err) {
      console.error('Failed to load tables for layout:', err);
      setError(`Failed to load tables: ${err instanceof Error ? err.message : 'Unknown error'}`);
    }
  };

  const handleTableClick = (table: TableWithStatus) => {
    // Only allow selecting available tables
    if (table.realStatus === 'available' && table.diningTableId !== currentTableId) {
      setSelectedDestinationId(table.diningTableId);
    }
  };

  const handleMove = async () => {
    if (!selectedDestinationId) {
      setError('Please select a destination table');
      return;
    }

    setIsMoving(true);
    setError(null);

    try {
      await moveOrderBetweenTables(currentTableId, selectedDestinationId);
      onMoveComplete();
      onClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to move order');
      console.error('Error moving order:', err);
    } finally {
      setIsMoving(false);
    }
  };

  const activeLayout = layouts.find(layout => layout.id.toString() === activeTab);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
     <div className="bg-white rounded-lg w-[90vw] h-[90vh] flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-gray-200 flex justify-between items-center">
          <h2 className="text-xl font-bold">Move Order to Another Table</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 p-1"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Error message */}
        {error && (
          <div className="mx-4 mt-2 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {error}
          </div>
        )}

        {/* Tabs */}
        {layouts.length > 0 && (
          <div className="bg-white border-b border-gray-200">
            <div className="flex overflow-x-auto">
              {layouts.map((layout) => (
                <button
                  key={layout.id}
                  onClick={() => setActiveTab(layout.id.toString())}
                  className={`flex-shrink-0 px-6 py-3 text-sm font-medium border-b-2 transition-colors whitespace-nowrap ${activeTab === layout.id.toString()
                    ? 'border-blue-500 text-blue-600 bg-blue-50'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                >
                  <div className="flex items-center space-x-2">
                    <span>{layout.name}</span>
                  </div>
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Content */}
        <div className="flex-1 flex overflow-hidden">
          {isLoading ? (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <RefreshCw className="w-8 h-8 mx-auto mb-2 animate-spin text-gray-400" />
                <p className="text-gray-500">Loading layouts...</p>
              </div>
            </div>
          ) : (
            <LayoutViewer
              layout={activeLayout || null}
              tablesWithStatus={tablesWithStatus}
              currentTableId={currentTableId}
              onTableClick={handleTableClick}
            />
          )}
        </div>

        {/* Instructions */}
        <div className="p-4 bg-blue-50 mx-4 mt-2 rounded-lg">
          <p className="text-blue-800">
            <span className="font-medium">Current table:</span> Highlighted in blue
          </p>
          <p className="text-blue-800 mt-1">
            <span className="font-medium">Available tables:</span> Highlighted in green (click to select)
          </p>
          <p className="text-blue-800 mt-1">
            <span className="font-medium">Occupied tables:</span> Highlighted in red (cannot be selected)
          </p>
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-gray-200 bg-gray-50">
          <div className="flex justify-between items-center">
            <div className="text-sm text-gray-600">
              {selectedDestinationId ? (
                <div className="font-medium text-green-600">
                  Destination table selected! Click "Move Order" to confirm.
                </div>
              ) : (
                <div>Click on an available (green) table to select it as the destination</div>
              )}
            </div>
            <div className="flex space-x-3">
              <button
                onClick={onClose}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50"
                disabled={isMoving}
              >
                Cancel
              </button>
              <button
                onClick={handleMove}
                disabled={!selectedDestinationId || isMoving}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center"
              >
                {isMoving ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Moving...
                  </>
                ) : (
                  'Move Order'
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
