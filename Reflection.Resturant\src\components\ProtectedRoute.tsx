import React from 'react';
import { Navigate } from 'react-router-dom';
import { usePermissions } from '../contexts/PermissionsContext';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredPermission: 'kitchen' | 'tableManagement' | 'pos' | 'invoices';
  fallbackPath?: string;
}

export function ProtectedRoute({ 
  children, 
  requiredPermission, 
  fallbackPath = '/' 
}: ProtectedRouteProps) {
  const { permissions, isLoading } = usePermissions();

  // Show loading while permissions are being fetched
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
          <p className="text-gray-600">Checking permissions...</p>
        </div>
      </div>
    );
  }

  // If no permissions loaded, redirect to fallback
  if (!permissions) {
    return <Navigate to={fallbackPath} replace />;
  }

  // Check specific permission
  let hasAccess = false;
  switch (requiredPermission) {
    case 'kitchen':
      hasAccess = permissions.canAccessKitchen;
      break;
    case 'tableManagement':
      hasAccess = permissions.canAccessTableManagement;
      break;
    case 'pos':
      hasAccess = permissions.canAccessPOS;
      break;
    case 'invoices':
      hasAccess = permissions.canAccessInvoices;
      break;
    default:
      hasAccess = false;
  }

  // If user doesn't have access, show access denied or redirect
  if (!hasAccess) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="text-red-500 text-6xl mb-4">🚫</div>
          <h2 className="text-2xl font-bold text-gray-800 mb-2">Access Denied</h2>
          <p className="text-gray-600 mb-4">
            You don't have permission to access this page.
          </p>
          <p className="text-sm text-gray-500">
            Required permission: {requiredPermission}
          </p>
        </div>
      </div>
    );
  }

  // User has access, render the protected content
  return <>{children}</>;
}
