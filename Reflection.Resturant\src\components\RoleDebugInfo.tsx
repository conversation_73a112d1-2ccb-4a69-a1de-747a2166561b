import React, { useState, useEffect } from 'react';
import { getRoleIdFromToken, getUserPermissions, getUserRoleForms } from '../services/roleService';
import { appUser } from '../services/authService';
import { usePermissions } from '../contexts/PermissionsContext';

export function RoleDebugInfo() {
  const [roleId, setRoleId] = useState<number | null>(null);
  const [roleForms, setRoleForms] = useState<any[]>([]);
  const [isVisible, setIsVisible] = useState(false);
  const { permissions, refreshPermissions } = usePermissions();

  useEffect(() => {
    const fetchRoleInfo = async () => {
      const extractedRoleId = getRoleIdFromToken();
      setRoleId(extractedRoleId);

      if (extractedRoleId) {
        const forms = await getUserRoleForms(extractedRoleId);
        setRoleForms(forms);
      }
    };

    if (appUser.identityToken) {
      fetchRoleInfo();
    }
  }, [appUser.identityToken]);

  const handleRefreshPermissions = async () => {
    await refreshPermissions();
    const forms = await getUserRoleForms();
    setRoleForms(forms);
  };

  if (!isVisible) {
    return (
      <div className="fixed bottom-4 right-4">
        <button
          onClick={() => setIsVisible(true)}
          className="bg-purple-600 text-white px-3 py-2 rounded-md text-sm hover:bg-purple-700"
        >
          Debug Role Info
        </button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg shadow-lg p-4 max-w-md max-h-96 overflow-y-auto">
      <div className="flex justify-between items-center mb-3">
        <h3 className="text-lg font-semibold text-gray-800">Role Debug Info</h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-500 hover:text-gray-700"
        >
          ✕
        </button>
      </div>

      <div className="space-y-3 text-sm">
        <div>
          <strong>User ID:</strong> {appUser.userId || 'Not set'}
        </div>
        
        <div>
          <strong>Username:</strong> {appUser.UserName || 'Not set'}
        </div>
        
        <div>
          <strong>Database:</strong> {appUser.DbName || 'Not set'}
        </div>
        
        <div>
          <strong>Has Token:</strong> {appUser.identityToken ? 'Yes' : 'No'}
        </div>
        
        <div>
          <strong>Extracted Role ID:</strong> {roleId || 'Not found'}
        </div>

        <div>
          <strong>Current Permissions:</strong>
          {permissions ? (
            <ul className="ml-4 mt-1">
              <li>Kitchen: {permissions.canAccessKitchen ? '✅' : '❌'}</li>
              <li>Table Management: {permissions.canAccessTableManagement ? '✅' : '❌'}</li>
              <li>POS: {permissions.canAccessPOS ? '✅' : '❌'}</li>
              <li>Invoices: {permissions.canAccessInvoices ? '✅' : '❌'}</li>
            </ul>
          ) : (
            <span className="text-gray-500">Loading...</span>
          )}
        </div>

        <div>
          <strong>Role Forms ({roleForms.length}):</strong>
          {roleForms.length > 0 ? (
            <ul className="ml-4 mt-1 space-y-1">
              {roleForms.map((form, index) => (
                <li key={index} className="text-xs">
                  {form.formName} (ID: {form.id})
                </li>
              ))}
            </ul>
          ) : (
            <span className="text-gray-500">No forms found</span>
          )}
        </div>

        <button
          onClick={handleRefreshPermissions}
          className="w-full bg-blue-600 text-white px-3 py-2 rounded-md text-sm hover:bg-blue-700"
        >
          Refresh Permissions
        </button>
      </div>
    </div>
  );
}
