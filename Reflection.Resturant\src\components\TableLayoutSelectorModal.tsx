import React, { useState, useEffect } from 'react';
import { X, RefreshCw, Eye } from 'lucide-react';
import { TableLayout, LayoutTable, DiningTable } from '../types';
import { getAllLayouts, getActiveLayout, getActiveLayouts } from '../services/layoutService';
import { getActiveDiningTables, initializeDefaultTables, updateDiningTableStatus, setActiveLayout, autoSaveOrderToTable, regenerateAllDiningTables } from '../services/tableService';
import { dbService } from '../services/indexedDBService';

interface TableLayoutSelectorModalProps {
  isOpen: boolean;
  onSelectTable: (tableId: string) => void; // Now accepts composite ID
  onClose: () => void;
}

interface TableWithStatus extends LayoutTable {
  diningTableId: string; // Now uses composite ID
  realStatus: DiningTable['status'];
  hasOrder: boolean;
  currentOrderId?: string;
  tableName: string;
}

interface LayoutViewerProps {
  layout: TableLayout | null;
  tablesWithStatus: TableWithStatus[];
  onTableClick: (table: TableWithStatus) => void;
}

const LayoutViewer: React.FC<LayoutViewerProps> = ({ layout, tablesWithStatus, onTableClick }) => {
  if (!layout) {
    return (
      <div className="flex-1 flex items-center justify-center text-gray-500">
        <div className="text-center">
          <Eye className="w-16 h-16 mx-auto mb-4 opacity-50" />
          <p className="text-xl">No layout available</p>
          <p className="text-sm">Please create a layout first</p>
        </div>
      </div>
    );
  }

  const getTableStatusColor = (table: TableWithStatus) => {
    switch (table.realStatus) {
      case 'available': return '#16a34a'; // green
      case 'occupied': return '#dc2626'; // red
      // case 'reserved': return '#f59e0b'; // yellow
      default: return table.borderColor;
    }
  };

  const getTableStatusBg = (table: TableWithStatus) => {
    switch (table.realStatus) {
      case 'available': return 'bg-green-50 hover:bg-green-100';
      case 'occupied': return 'bg-red-50 hover:bg-red-100';
      // case 'reserved': return 'bg-yellow-50 hover:bg-yellow-100';
      default: return 'bg-white hover:bg-gray-50';
    }
  };

  const getTableStatusText = (table: TableWithStatus) => {
    if (table.hasOrder) return '📋 Order';
    switch (table.realStatus) {
      case 'occupied': return '🔒 Occupied';
      // case 'reserved': return '📅 Reserved';
      case 'available': return '✅ Available';
      default: return '';
    }
  };

  return (
    <div className="flex-1 relative overflow-auto bg-gray-50"
      style={{
        height: '600px',
        width: '800px',
        // maxWidth: '1200px',
        overflow: 'auto',
        position: 'relative'
      }}>
      <div
        className="min-w-full min-h-full relative"
        style={{
          backgroundImage: layout.floorPlanImage ? `url(${layout.floorPlanImage})` : 'none',
          backgroundSize: 'contain',
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'left top',
          minHeight: '800px',
          minWidth: '1200px',
        }}
      >
        {/* Grid overlay when no image */}
        {!layout.floorPlanImage && (
          <div
            className="absolute inset-0 opacity-5"
            style={{
              backgroundImage: `
                linear-gradient(rgba(0,0,0,0.3) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0,0,0,0.3) 1px, transparent 1px)
              `,
              backgroundSize: '20px 20px'
            }}
          />
        )}

        {/* Tables */}
        {tablesWithStatus.map(table => {
          console.log(`🎯 Table ${table.number}: x=${table.x}, y=${table.y}, left=${table.x}, top=${table.y}, size=${table.width}x${table.height}`);
          return (
            <div
              key={table.id}
              className="absolute cursor-pointer select-none z-10 hover:z-20 transition-all hover:scale-105 group"
              style={{
                left: table.x,
                top: table.y,
                width: table.width,
                height: table.height,
              }}
              onClick={() => onTableClick(table)}
              title={`Table ${table.number} - ${table.realStatus}${table.hasOrder ? ' (Has Order)' : ''}`}
            >
              {/* Table */}
              <div
                className={`w-full h-full rounded-lg border-4 flex items-center justify-center
                         font-bold text-lg shadow-lg bg-opacity-90 backdrop-blur-sm bg-transparent
                         hover:shadow-xl hover:bg-opacity-95 hover:border-opacity-80 transition-all
                         group-hover:scale-105 ${getTableStatusBg(table)}
                         ${table.hasOrder ? 'ring-4 ring-blue-400 ring-opacity-60' : ''}
                         ${table.realStatus === 'occupied' ? 'ring-4 ring-red-400 ring-opacity-60' : ''}
                         `
                        }
                style={{
                  borderColor: table.borderColor,
                  //getTableStatusColor(table),
                  color: getTableStatusColor(table),
                  borderWidth: table.realStatus !== 'available' ? '6px' : '4px'
                }}>
                <div className="text-center">
                  <div className="text-xl font-bold">{table.number}</div>
                  <div className="text-xs opacity-80">{table.seats} seats</div>
                  <div className="text-xs mt-1 px-2 py-1 bg-white bg-opacity-80 rounded font-medium">
                    {getTableStatusText(table)}
                  </div>
                </div>
              </div>

              {/* Status indicator */}
              <div
                className={`absolute -top-3 -left-3 w-6 h-6 rounded-full border-3 border-white shadow-lg
                         ${table.realStatus === 'occupied' ? 'animate-pulse' : ''}
                         ${table.hasOrder ? 'animate-bounce' : ''}`}
                style={{
                  backgroundColor: getTableStatusColor(table),
                  borderWidth: '3px'
                }}
              >
                {table.hasOrder && (
                  <div className="absolute inset-0 flex items-center justify-center text-white text-xs font-bold">
                    📋
                  </div>
                )}
              </div>
            </div>
          );
        })}

        {/* Layout info overlay */}
        {/* <div className="absolute top-4 left-4 bg-white bg-opacity-90 backdrop-blur-sm rounded-lg p-3 shadow-md">
          <h3 className="font-bold text-gray-800">{layout.name}</h3>
          <p className="text-sm text-gray-600">{tablesWithStatus.length} tables</p>
          <div className="flex items-center space-x-4 mt-2 text-xs">
            <div className="flex items-center space-x-1">
              <div className="w-3 h-3 rounded-full bg-green-500"></div>
              <span>Available</span>
            </div>
            <div className="flex items-center space-x-1">
              <div className="w-3 h-3 rounded-full bg-red-500"></div>
              <span>Occupied</span>
            </div>
            <div className="flex items-center space-x-1">
              <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
              <span>Reserved</span>
            </div>
          </div>
        </div> */}
      </div>
    </div>
  );
};

export function TableLayoutSelectorModal({ isOpen, onSelectTable, onClose }: TableLayoutSelectorModalProps) {
  const [layouts, setLayouts] = useState<TableLayout[]>([]);
  const [activeLayouts, setActiveLayouts] = useState<TableLayout[]>([]);
  const [activeTab, setActiveTab] = useState<string | null>(null);
  const [tablesWithStatus, setTablesWithStatus] = useState<TableWithStatus[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (isOpen) {
      loadData();
    }
  }, [isOpen]);

  useEffect(() => {
    if (activeTab) {
      loadTablesForLayout(activeTab);
    }
  }, [activeTab]);

  const loadData = async () => {
    setIsLoading(true);
    setError(null);

    try {
      console.log('Loading layouts and active layout...');

      // Try to load layouts first
      let savedLayouts: TableLayout[] = [];
      try {
        savedLayouts = await getAllLayouts();
        console.log('Layouts loaded successfully:', savedLayouts);

        // If no layouts exist, try to initialize default tables
        if (savedLayouts.length === 0) {
          console.log('No layouts found, initializing default tables...');
          await initializeDefaultTables();
          savedLayouts = await getAllLayouts();
          console.log('Layouts after initialization:', savedLayouts);
        }
      } catch (layoutError) {
        console.error('Error loading layouts:', layoutError);
        throw new Error(`Failed to load layouts: ${layoutError instanceof Error ? layoutError.message : 'Unknown error'}`);
      }

      // Try to get active layouts (this might fail due to IndexedDB issues)
      let activeLayoutsList: TableLayout[] = [];
      try {
        activeLayoutsList = await getActiveLayouts();
        console.log('Active layouts loaded:', activeLayoutsList);
      } catch (activeLayoutError) {
        console.warn('Error loading active layouts (will continue without it):', activeLayoutError);
        // Fallback: find active layouts manually from the loaded layouts
        activeLayoutsList = savedLayouts.filter(layout => layout.isActive);
        console.log('Fallback active layouts:', activeLayoutsList);
      }

      const sortedLayouts = savedLayouts.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
      setLayouts(sortedLayouts);
      setActiveLayouts(activeLayoutsList);

      // Set first active layout as default, or first layout if no active layouts
      const defaultTab = activeLayoutsList.length > 0 ? activeLayoutsList[0].id : (sortedLayouts.length > 0 ? sortedLayouts[0].id : null);
      if (defaultTab) {
        setActiveTab(defaultTab.toString());
        console.log('Set default tab to:', defaultTab);
      } else {
        console.warn('No layouts available to set as default tab');
      }
    } catch (err) {
      console.error('Failed to load layouts:', err);
      setError(`Failed to load layouts: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setIsLoading(false);
    }
  };

  const loadTablesForLayout = async (layoutId: string) => {
    try {
      const layout = layouts.find(l => l.id.toString() === layoutId);
      if (!layout) {
        console.warn('Layout not found:', layoutId);
        setTablesWithStatus([]);
        return;
      }

      console.log(`📋 Loading tables for layout: ${layout.name} (ID: ${layout.id})`);
      console.log(`🖼️ Layout floor plan image: ${layout.floorPlanImage ? 'YES' : 'NO'}`);
      const diningTables = await getActiveDiningTables();
      console.log('🍽️ Dining tables loaded:', diningTables);
      console.log('📊 Layout tables:', layout.tables);

      // Map layout tables to dining tables and get their status
      const tablesWithStatusPromises = layout.tables.map(async (layoutTable) => {
        // Find corresponding dining table using composite ID
        const compositeId = `${layout.id}-${layoutTable.id}`;
        const diningTable = diningTables.find(dt => dt.id === compositeId);

        if (diningTable) {
          const hasOrder = !!diningTable.currentOrderId;
          console.log(`✓ Table ${layoutTable.number}: Found dining table ID=${diningTable.id}, status=${diningTable.status}, hasOrder=${hasOrder}, currentOrderId=${diningTable.currentOrderId}`);
          return {
            ...layoutTable,
            diningTableId: diningTable.id,
            realStatus: diningTable.status,
            hasOrder,
            currentOrderId: diningTable.currentOrderId,
            tableName: `${layout.name} - ${layoutTable.number}`
          } as TableWithStatus;
        } else {
          // If no dining table found, create a virtual one
          console.log(`⚠️ Table ${layoutTable.number}: No dining table found, creating virtual table`);
          return {
            ...layoutTable,
            diningTableId: compositeId,
            realStatus: 'available' as const,
            hasOrder: false,
            tableName: `${layout.name} - ${layoutTable.number}`
          } as TableWithStatus;
        }
      });

      const tablesWithStatusData = await Promise.all(tablesWithStatusPromises);
      console.log('📊 Final tables with status mapping:');
      tablesWithStatusData.forEach(table => {
        console.log(`  Table ${table.number}: status=${table.realStatus}, hasOrder=${table.hasOrder}, diningTableId=${table.diningTableId}, tableName=${table.tableName}`);
      });
      setTablesWithStatus(tablesWithStatusData);
    } catch (err) {
      console.error('Failed to load table statuses:', err);
      setError('Failed to load table statuses. Please try refreshing.');
    }
  };

  const handleTableClick = async (table: TableWithStatus) => {
    try {
      console.log('Table selected:', {
        tableNumber: table.number,
        diningTableId: table.diningTableId,
        status: table.realStatus,
        hasOrder: table.hasOrder,
        currentOrderId: table.currentOrderId
      });

      // If table is occupied and has an order, we'll let the POS page handle loading the order
      // The POS page's handleTableSelect function already handles this case
      onSelectTable(table.diningTableId); // onSelectTable(table.diningTableId);
    } catch (err) {
      console.error('Failed to select table:', err);
    }
  };

  const debugDatabase = async () => {
    try {
      console.log('=== Database Debug ===');
      await dbService.init();
      await dbService.debugDatabaseSchema();

      console.log('Testing getAllLayouts...');
      const layouts = await getAllLayouts();
      console.log('Layouts:', layouts);

      console.log('Testing getActiveLayout...');
      const activeLayout = await getActiveLayout();
      console.log('Active layout:', activeLayout);

      console.log('Testing initializeDefaultTables...');
      await initializeDefaultTables();

      console.log('Re-testing getAllLayouts after initialization...');
      const layoutsAfter = await getAllLayouts();
      console.log('Layouts after init:', layoutsAfter);

    } catch (err) {
      console.error('Database debug failed:', err);
    }
  };

  const resetDatabase = async () => {
    try {
      console.log('=== Resetting Database ===');
      await dbService.resetDatabase();
      console.log('Database reset complete, reinitializing...');
      await dbService.init();
      await initializeDefaultTables();
      console.log('Database reinitialized, reloading data...');
      await loadData();
    } catch (err) {
      console.error('Database reset failed:', err);
    }
  };

  const createTestOccupiedTables = async () => {
    try {
      console.log('=== Creating Test Occupied Tables ===');

      // First ensure we have an active layout
      const activeLayout = await getActiveLayout();
      if (!activeLayout) {
        console.log('No active layout found, initializing default tables...');
        await initializeDefaultTables();
      }

      const diningTables = await getActiveDiningTables();
      console.log('Available dining tables:', diningTables);

      if (diningTables.length === 0) {
        console.log('No dining tables found, this might be the issue!');
        console.log('Trying to regenerate dining tables from active layout...');
        const layout = await getActiveLayout();
        if (layout) {
          // Force regenerate dining tables
          await setActiveLayout(layout.id.toString());
          const newDiningTables = await getActiveDiningTables();
          console.log('Regenerated dining tables:', newDiningTables);
        }
        return;
      }

      if (diningTables.length >= 1) {
        // Make table 1 occupied with an order
        await updateDiningTableStatus(diningTables[0].id, 'occupied', 'test-order-1');
        console.log(`✅ Set table ${diningTables[0].number} (ID: ${diningTables[0].id}) as occupied with order`);

        if (diningTables.length >= 2) {
          // Make table 2 just occupied (no order)
          await updateDiningTableStatus(diningTables[1].id, 'occupied');
          console.log(`✅ Set table ${diningTables[1].number} (ID: ${diningTables[1].id}) as occupied without order`);
        }

        if (diningTables.length >= 3) {
          // Make table 3 reserved
          await updateDiningTableStatus(diningTables[2].id, 'reserved');
          console.log(`✅ Set table ${diningTables[2].number} (ID: ${diningTables[2].id}) as reserved`);
        }

        // Verify the changes
        const updatedTables = await getActiveDiningTables();
        console.log('📊 Updated dining tables after status changes:');
        updatedTables.forEach(table => {
          console.log(`  Table ${table.number}: status=${table.status}, currentOrderId=${table.currentOrderId}`);
        });

        // Reload the table data in the modal
        if (activeTab) {
          await loadTablesForLayout(activeTab);
        }
      } else {
        console.log('⚠️ Not enough dining tables to create test data');
      }
    } catch (err) {
      console.error('❌ Failed to create test occupied tables:', err);
    }
  };

  const fixMissingTables = async () => {
    try {
      console.log('🔧 Fixing missing dining tables...');
      await regenerateAllDiningTables();
      console.log('✅ All dining tables regenerated successfully');

      // Reload the layouts and tables
      await loadData();
      if (activeTab) {
        await loadTablesForLayout(activeTab);
      }
    } catch (err) {
      console.error('❌ Failed to fix missing tables:', err);
    }
  };

  const createTestOrdersForTables = async () => {
    try {
      console.log('=== Creating Test Orders for Tables ===');

      const diningTables = await getActiveDiningTables();
      console.log('Available dining tables:', diningTables);

      if (diningTables.length >= 1) {
        // Create a test order for the first table
        const testItems = [
          {
            id: 1,
            familyId: 1,
            name: 'Test Burger',
            abbreviation: 'BURGER',
            stmp: Date.now(),
            image: '',
            isDeleted: false,
            deactivated: false,
            notSubjectToDiscount: false,
            discount: 0,
            cosmeticTax: 0,
            vat: 0,
            tax1Amount: 0,
            tax2Percentage: 0,
            isExpiry: false,
            price: 12.99,
            quantity: 2
          },
          {
            id: 2,
            familyId: 1,
            name: 'Test Fries',
            abbreviation: 'FRIES',
            stmp: Date.now(),
            image: '',
            isDeleted: false,
            deactivated: false,
            notSubjectToDiscount: false,
            discount: 0,
            cosmeticTax: 0,
            vat: 0,
            tax1Amount: 0,
            tax2Percentage: 0,
            isExpiry: false,
            price: 4.99,
            quantity: 1
          }
        ];

        const testCustomerInfo = {
          name: 'Test Customer',
          phone: '************'
        };

        console.log(`Creating test order for table ${diningTables[0].number}...`);
        const orderId = await autoSaveOrderToTable(
          diningTables[0].id,
          testItems,
          testCustomerInfo,
          10, // 10% discount
          'percentage'
        );

        console.log(`✅ Created test order ${orderId} for table ${diningTables[0].number}`);

        // Reload the table data to show the updated status
        if (activeTab) {
          await loadTablesForLayout(activeTab);
        }
      } else {
        console.log('⚠️ No dining tables available to create test orders');
      }
    } catch (err) {
      console.error('❌ Failed to create test orders:', err);
    }
  };

  const activeLayout = layouts.find(layout => layout.id.toString() === activeTab);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-[90vw] h-[80vh] flex flex-col">
        {/* Header */}
        <div className="flex justify-between items-center p-4 border-b border-gray-200">
          <h2 className="text-xl font-semibold">Select Table</h2>
          <div className="flex items-center space-x-2">
            <button
              onClick={loadData}
              disabled={isLoading}
              className="p-2 text-gray-500 hover:text-gray-700 transition-colors"
            >
              <RefreshCw className={`w-5 h-5 ${isLoading ? 'animate-spin' : ''}`} />
            </button>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        {error && (
          <div className="p-4 bg-red-50 border-b border-red-200">
            <p className="text-red-600 text-sm">{error}</p>
            <div className="mt-2 space-x-2">
              <button
                onClick={loadData}
                className="px-3 py-1 bg-red-600 text-white text-xs rounded hover:bg-red-700 transition-colors"
              >
                Retry
              </button>
              <button
                onClick={debugDatabase}
                className="px-3 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700 transition-colors"
              >
                Debug DB
              </button>
              <button
                onClick={resetDatabase}
                className="px-3 py-1 bg-yellow-600 text-white text-xs rounded hover:bg-yellow-700 transition-colors"
              >
                Reset DB
              </button>
              <button
                onClick={createTestOccupiedTables}
                className="px-3 py-1 bg-purple-600 text-white text-xs rounded hover:bg-purple-700 transition-colors"
              >
                Test Tables
              </button>
              <button
                onClick={createTestOrdersForTables}
                className="px-3 py-1 bg-green-600 text-white text-xs rounded hover:bg-green-700 transition-colors"
              >
                Test Orders
              </button>
              <button
                onClick={fixMissingTables}
                className="px-3 py-1 bg-red-600 text-white text-xs rounded hover:bg-red-700 transition-colors"
              >
                Fix Missing Tables
              </button>
            </div>
          </div>
        )}

        {/* Tabs */}
        {layouts.length > 0 && (
          <div className="bg-white border-b border-gray-200">
            <div className="flex overflow-x-auto">
              {/* Show active layouts first, then inactive ones */}
              {/* {[...activeLayouts, ...layouts.filter(l => !l.isActive)].map((layout) => ( */}
              {layouts.map((layout) => (
                <button
                  key={layout.id}
                  onClick={() => setActiveTab(layout.id.toString())}
                  className={`flex-shrink-0 px-6 py-3 text-sm font-medium border-b-2 transition-colors whitespace-nowrap ${activeTab === layout.id.toString()
                    ? 'border-blue-500 text-blue-600 bg-blue-50'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                >
                  <div className="flex items-center space-x-2">
                    <span>{layout.name}</span>
                    {/* <span className="text-xs bg-gray-200 px-2 py-1 rounded-full">
                      {(layout.tables || []).length}
                    </span>
                    {layout.isActive && (
                      <span className="text-xs bg-green-200 text-green-800 px-2 py-1 rounded-full">
                        Active
                      </span>
                    )} */}
                  </div>
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Content */}
        <div className="flex-1 flex">
          {isLoading ? (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <RefreshCw className="w-8 h-8 mx-auto mb-2 animate-spin text-gray-400" />
                <p className="text-gray-500">Loading layouts...</p>
              </div>
            </div>
          ) : (
            <LayoutViewer
              layout={activeLayout || null}
              tablesWithStatus={tablesWithStatus}
              onTableClick={handleTableClick}
            />
          )}
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-gray-200 bg-gray-50">
          <div className="flex justify-between items-center">
            <div className="text-sm text-gray-600">
              <div>Click on a table to select it for your order</div>
              <div className="text-xs text-gray-500 mt-1">
                📋 Tables with orders will load existing order • 🟢 Available • 🔴 Occupied
              </div>
            </div>
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
