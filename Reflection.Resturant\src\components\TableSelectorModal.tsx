
import { DiningTable } from '../types';

interface TableSelectorModalProps {
  isOpen: boolean;
  tables: DiningTable[];
  onSelectTable: (tableId: string) => void;
  onClose: () => void;
}

export function TableSelectorModal({ isOpen, tables, onSelectTable, onClose }: TableSelectorModalProps) {
  if (!isOpen) return null;

  const getStatusColor = (status: DiningTable['status']) => {
    switch (status) {
      case 'available': return 'bg-green-100 text-green-800 border-green-300';
      case 'occupied': return 'bg-red-100 text-red-800 border-red-300';
      case 'reserved': return 'bg-yellow-100 text-yellow-800 border-yellow-300';
      default: return 'bg-gray-100 text-gray-800 border-gray-300';
    }
  };

  const availableTables = tables.filter(table => table.status === 'available');
  const occupiedTables = tables.filter(table => table.status === 'occupied');

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-96 max-h-96 overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">Select Table</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Available Tables */}
        <div className="mb-4">
          <h4 className="text-sm font-medium text-gray-700 mb-2">Available Tables</h4>
          {availableTables.length === 0 ? (
            <p className="text-sm text-gray-500">No available tables</p>
          ) : (
            <div className="grid grid-cols-3 gap-2">
              {availableTables.map(table => (
                <button
                  key={table.id}
                  onClick={() => onSelectTable(table.id.toString())}
                  className={`p-3 border-2 rounded-lg text-center hover:bg-green-50 transition-colors ${getStatusColor(table.status)}`}
                >
                  <div className="font-medium">Table {table.number}</div>
                  <div className="text-xs">{table.seats} seats</div>
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Occupied Tables */}
        {occupiedTables.length > 0 && (
          <div className="mb-4">
            <h4 className="text-sm font-medium text-gray-700 mb-2">Occupied Tables (with existing orders)</h4>
            <div className="grid grid-cols-3 gap-2">
              {occupiedTables.map(table => (
                <button
                  key={table.id}
                  onClick={() => onSelectTable(table.id.toString())}
                  className={`p-3 border-2 rounded-lg text-center hover:bg-red-50 transition-colors ${getStatusColor(table.status)}`}
                >
                  <div className="font-medium">Table {table.number}</div>
                  <div className="text-xs">{table.seats} seats</div>
                  <div className="text-xs mt-1">Has Order</div>
                </button>
              ))}
            </div>
          </div>
        )}

        <div className="flex justify-end mt-4">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  );
}
