import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { UserPermissions, getUserPermissions } from '../services/roleService';

interface PermissionsContextType {
  permissions: UserPermissions | null;
  isLoading: boolean;
  refreshPermissions: () => Promise<void>;
}

const PermissionsContext = createContext<PermissionsContextType | undefined>(undefined);

export function usePermissions() {
  const context = useContext(PermissionsContext);
  if (context === undefined) {
    throw new Error('usePermissions must be used within a PermissionsProvider');
  }
  return context;
}

interface PermissionsProviderProps {
  children: ReactNode;
  user: { username: string; role: string } | null;
}

export function PermissionsProvider({ children, user }: PermissionsProviderProps) {
  const [permissions, setPermissions] = useState<UserPermissions | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const refreshPermissions = async () => {
    if (!user) {
      setPermissions(null);
      return;
    }

    setIsLoading(true);
    try {
      const userPermissions = await getUserPermissions();
      setPermissions(userPermissions);
      console.log('✅ Permissions refreshed:', userPermissions);
    } catch (error) {
      console.error('❌ Failed to refresh permissions:', error);
      // Set default minimal permissions on error
      setPermissions({
        canAccessKitchen: false,
        canAccessTableManagement: false,
        canAccessPOS: false,
        canAccessInvoices: true,
        forms: []
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Load permissions when user changes
  useEffect(() => {
    refreshPermissions();
  }, [user]);

  const value: PermissionsContextType = {
    permissions,
    isLoading,
    refreshPermissions
  };

  return (
    <PermissionsContext.Provider value={value}>
      {children}
    </PermissionsContext.Provider>
  );
}
