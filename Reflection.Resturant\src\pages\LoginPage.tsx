import React, { useState } from 'react';
import { login, signIn, appUser, Database } from '../services/authService';
import { DatabaseSelectionModal } from '../components/DatabaseSelectionModal';

export function LoginPage({ onLogin }: { onLogin: (user: { username: string; role: string }) => void }) {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showDatabaseModal, setShowDatabaseModal] = useState(false);
  const [availableDatabases, setAvailableDatabases] = useState<Database[]>([]);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      // Step 1: Login with RSA
      const loginResult = await login(username, password);

      if (!loginResult.success) {
        setError('Login failed. Please check your credentials.');
        setIsLoading(false);
        return;
      }

      if (loginResult.isLogged) {
          setError('Login failed. User is already logged in.');
        // User is already logged in, proceed directly
        // onLogin({ username, role: 'user' });
        setIsLoading(false);
        return;
      }

      // Check databases
      if (loginResult.dbList.length === 0) {
        setError('No databases available for this user.');
        setIsLoading(false);
        return;
      }

      if (loginResult.dbList.length === 1) {
        // Only one database, connect directly
        appUser.DbName = loginResult.dbList[0].source;
        await handleSignIn();
      } else {
        // Multiple databases, show selection modal
        setAvailableDatabases(loginResult.dbList);
        setShowDatabaseModal(true);
        setIsLoading(false);
      }
    } catch (error) {
      console.error('Login error:', error);
      setError('An error occurred during login. Please try again.');
      setIsLoading(false);
    }
  };

  const handleDatabaseSelect = async (database: Database) => {
    setShowDatabaseModal(false);
    setIsLoading(true);
    appUser.DbName = database.source;
    await handleSignIn();
  };

  const handleSignIn = async () => {
    try {
      // Step 2: Generate token with selected database
      const signInResult = await signIn(username, password);

      if (signInResult.isOk && signInResult.data) {
        onLogin({ username, role: 'user' });
      } else {
        setError(signInResult.message || 'Authentication failed. Please try again.');
      }
    } catch (error) {
      console.error('SignIn error:', error);
      setError('An error occurred during authentication. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCloseModal = () => {
    setShowDatabaseModal(false);
    setIsLoading(false);
  };

  return (
    <>
      <div className="flex items-center justify-center h-full">
        <form onSubmit={handleSubmit} className="p-8 bg-white rounded shadow-md w-96">
          <h2 className="text-2xl font-bold mb-4">Login</h2>

          {error && (
            <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
              {error}
            </div>
          )}

          <div className="mb-4">
            <label className="block mb-2">Username</label>
            <input
              type="text"
              className="w-full p-2 border rounded"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              disabled={isLoading}
              required
            />
          </div>
          <div className="mb-4">
            <label className="block mb-2">Password</label>
            <input
              type="password"
              className="w-full p-2 border rounded"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              disabled={isLoading}
              required
            />
          </div>
          <button
            type="submit"
            className="w-full bg-blue-600 text-white p-2 rounded hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
            disabled={isLoading}
          >
            {isLoading ? 'Logging in...' : 'Login'}
          </button>
        </form>
      </div>

      <DatabaseSelectionModal
        isOpen={showDatabaseModal}
        databases={availableDatabases}
        onSelect={handleDatabaseSelect}
        onClose={handleCloseModal}
      />
    </>
  );
}