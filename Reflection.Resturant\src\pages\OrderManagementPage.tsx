import React, { useState, useEffect } from 'react';
import { Order } from '../types';
import { getAllOrders, updateOrderStatus } from '../services/orderService';

export function OrderManagementPage() {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string>('');

  // Load orders from IndexedDB
  useEffect(() => {
    loadOrders();
    // Set up auto-refresh every 30 seconds for real-time updates
    const interval = setInterval(loadOrders, 30000);
    return () => clearInterval(interval);
  }, []);

  const loadOrders = async () => {
    try {
      setLoading(true);
      setError('');
      const ordersData = await getAllOrders();
      // Filter only active orders (pending, preparing, ready)
      const activeOrders = ordersData.filter(order =>
        ['pending', 'preparing', 'ready'].includes(order.status)
      );
      setOrders(activeOrders);
    } catch (error) {
      console.error('Error loading orders:', error);
      setError('Failed to load orders. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle status change with optimistic updates
  const handleStatusChange = async (orderId: string, newStatus: Order['status']) => {
    try {
      // Optimistic update
      setOrders(prevOrders =>
        prevOrders.map(order =>
          order.id === orderId ? { ...order, status: newStatus } : order
        )
      );

      await updateOrderStatus(orderId, newStatus);
    } catch (error) {
      console.error('Error updating order status:', error);
      alert('Failed to update order status. Please try again.');
      // Revert optimistic update on error
      await loadOrders();
    }
  };

  // Group orders by status for kitchen columns
  const pendingOrders = orders.filter(order => order.status === 'pending');
  const preparingOrders = orders.filter(order => order.status === 'preparing');
  const readyOrders = orders.filter(order => order.status === 'ready');

  // Order card component
  const OrderCard = ({ order }: { order: Order }) => (
    <div className="bg-white rounded-lg shadow-md p-2 mb-2 border-l-4 border-blue-500">
      <div className="flex justify-between items-start mb-1">
        <div>
          <h3 className="font-bold text-lg text-gray-900">#{order.orderNumber}</h3>
          <p className="text-sm text-gray-600">{order.time} • {order.orderType} • {order.tableName && (
            order.tableName
          )}</p>
          {/* {order.tableName && (
            <p className="text-sm text-blue-600 font-medium">{order.tableName}</p>
          )} */}
        </div>
        <div className="text-right">
          {/* <p className="text-sm font-medium text-gray-700 mb-1">Customer</p> */}
          <p className="text-sm text-gray-600">{order.customerInfo.name || 'Walk-in Customer'}</p>
          {order.customerInfo.phone && (
            <p className="text-sm text-gray-600">{order.customerInfo.phone}</p>
          )}
          {/* <p className="font-bold text-lg text-gray-900">
            {formatPrice(order.total, defaultCurrency || undefined)}
          </p> */}
          <p className="text-sm text-gray-600">{order.items.length} items</p>
        </div>
      </div>

      {/* <div className="mb-3">
        <p className="text-sm font-medium text-gray-700 mb-1">Customer:</p>
        <p className="text-sm text-gray-600">{order.customerInfo.name || 'Walk-in Customer'}</p>
        {order.customerInfo.phone && (
          <p className="text-sm text-gray-600">{order.customerInfo.phone}</p>
        )}
      </div> */}

      <div className="mb-1">
        <p className="text-sm font-medium text-gray-700 mb-1">Items:</p>
        <div className="space-y-1">
          {order.items.map((item, index) => (
            <div key={index} className="flex justify-between text-sm">
              <span className="text-gray-600">{item.quantity}x {item.name}</span>
              {/* <span className="text-gray-900 font-medium">
                {formatPrice((item.price || 0) * item.quantity, defaultCurrency || undefined)}
              </span> */}
            </div>
          ))}
        </div>
      </div>

      <div className="flex gap-2">
        {order.status === 'pending' && (
          <button
            onClick={() => handleStatusChange(order.id, 'preparing')}
            className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors font-medium"
          >
            Start Preparing
          </button>
        )}

        {order.status === 'preparing' && (
          <>
            <button
              onClick={() => handleStatusChange(order.id, 'pending')}
              className="flex-1 bg-gray-500 text-white py-2 px-4 rounded-md hover:bg-gray-600 transition-colors font-medium"
            >
              Back to Pending
            </button>
            <button
              onClick={() => handleStatusChange(order.id, 'ready')}
              className="flex-1 bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 transition-colors font-medium"
            >
              Mark Ready
            </button>
          </>
        )}

        {order.status === 'ready' && (
          <button
            onClick={() => handleStatusChange(order.id, 'preparing')}
            className="flex-1 bg-yellow-600 text-white py-2 px-4 rounded-md hover:bg-yellow-700 transition-colors font-medium"
          >
            Back to Preparing
          </button>
        )}
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading orders...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="text-red-600 mb-4">
            <svg className="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <p className="text-lg font-semibold">Error Loading Orders</p>
            <p className="text-sm text-gray-600 mt-1">{error}</p>
          </div>
          <button
            onClick={loadOrders}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-1 bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="mb-2 flex justify-between items-center">
        <h1 className="text-3xl font-bold text-gray-900">Kitchen Orders</h1>
        <div className="flex items-center gap-2">
          <span className="text-gray-600">Auto-refresh: 30s</span>
          <button
            onClick={loadOrders}
            className="text-blue-600 hover:text-blue-800 font-medium"
          >
            Refresh Now
          </button>
        </div>
      </div>


      {/* Statistics */}
      {/* <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-yellow-50 border border-yellow-200 p-4 rounded-lg">
          <h3 className="text-sm font-medium text-yellow-800">Pending Orders</h3>
          <p className="text-2xl font-bold text-yellow-900">{pendingOrders.length}</p>
        </div>
        <div className="bg-blue-50 border border-blue-200 p-4 rounded-lg">
          <h3 className="text-sm font-medium text-blue-800">Preparing Orders</h3>
          <p className="text-2xl font-bold text-blue-900">{preparingOrders.length}</p>
        </div>
        <div className="bg-green-50 border border-green-200 p-4 rounded-lg">
          <h3 className="text-sm font-medium text-green-800">Ready Orders</h3>
          <p className="text-2xl font-bold text-green-900">{readyOrders.length}</p>
        </div>
      </div> */}

      {/* Kitchen Columns */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
        {/* Pending Orders Column */}
        <div className="bg-yellow-50 rounded-lg border border-yellow-200">
          <div className="bg-yellow-100 px-4 py-3 rounded-t-lg border-b border-yellow-200">
            <h2 className="text-lg font-semibold text-yellow-800 flex items-center">
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Pending ({pendingOrders.length})
            </h2>
          </div>
          <div className="p-4 max-h-[calc(100vh-200px)] overflow-y-auto">
            {pendingOrders.length === 0 ? (
              <div className="text-center py-8">
                <svg className="w-12 h-12 mx-auto text-yellow-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
                <p className="text-yellow-600">No pending orders</p>
              </div>
            ) : (
              pendingOrders.map(order => <OrderCard key={order.id} order={order} />)
            )}
          </div>
        </div>

        {/* Preparing Orders Column */}
        <div className="bg-blue-50 rounded-lg border border-blue-200">
          <div className="bg-blue-100 px-4 py-3 rounded-t-lg border-b border-blue-200">
            <h2 className="text-lg font-semibold text-blue-800 flex items-center">
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
              </svg>
              Preparing ({preparingOrders.length})
            </h2>
          </div>
          <div className="p-4 max-h-[calc(100vh-200px)] overflow-y-auto">
            {preparingOrders.length === 0 ? (
              <div className="text-center py-8">
                <svg className="w-12 h-12 mx-auto text-blue-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                </svg>
                <p className="text-blue-600">No orders being prepared</p>
              </div>
            ) : (
              preparingOrders.map(order => <OrderCard key={order.id} order={order} />)
            )}
          </div>
        </div>

        {/* Ready Orders Column */}
        <div className="bg-green-50 rounded-lg border border-green-200">
          <div className="bg-green-100 px-4 py-3 rounded-t-lg border-b border-green-200">
            <h2 className="text-lg font-semibold text-green-800 flex items-center">
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Ready ({readyOrders.length})
            </h2>
          </div>
          <div className="p-4 max-h-[calc(100vh-200px)] overflow-y-auto">
            {readyOrders.length === 0 ? (
              <div className="text-center py-8">
                <svg className="w-12 h-12 mx-auto text-green-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <p className="text-green-600">No orders ready</p>
              </div>
            ) : (
              readyOrders.map(order => <OrderCard key={order.id} order={order} />)
            )}
          </div>
        </div>
      </div>
    </div>
  );
}