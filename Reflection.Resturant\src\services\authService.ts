import { JSEncrypt } from 'jsencrypt';
import { saveUserSession, clearUserSession } from './persistentAuthService';

// Environment variables - these should be moved to .env file
const IdentityUrl = 'http://*************:2828';
const ApplicationId = '9';// process.env.REACT_APP_APPLICATION_ID?.toString() || '';
const IdentityKey = '12345'; // process.env.REACT_APP_IDENTITY_KEY?.toString() || '';

// RSA Keys
export const rsaPubKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCYlZyI5AQa6vIYWoJjKqG5tTVar/oU8qtFMEPtbjXG2ZTklkO/ugPzkn6C5uU2nbL18PhXTy05nyznT2T844FiEJ73O/sY8saPspmWEBiLJEuSuM/G8+6DRZYUxfkv7QVnMkHQG5mmWSIcWQepPexXJvr2u8e/9VHimNyvnmdS8QIDAQAB";

export const rsaPrivateKey = 'MIICXAIBAAKBgQCYlZyI5AQa6vIYWoJjKqG5tTVar/oU8qtFMEPtbjXG2ZTklkO/ugPzkn6C5uU2nbL18PhXTy05nyznT2T844FiEJ73O/sY8saPspmWEBiLJEuSuM/G8+6DRZYUxfkv7QVnMkHQG5mmWSIcWQepPexXJvr2u8e/9VHimNyvnmdS8QIDAQABAoGAVmqdfhYNkSUxOcPBBuL1N1U+w37ZFxYdFBFy1zzM7jL6lb/Y3PRAGqoERqT4ekc0PQBd1iz8gx74coTPwD4lXvvESBOQxsOigEu4sV62Lh4E+cVo7fSLuRgbO03arMz9a/MTnehlMWjjdPevw2GwxSSZwJ/U4ttJ06s7Aw2pgAECQQD6NpBdzIa7KvK1FxLCxC0DkIJDTcBGBgB75U3TdoE4kcwET4xePlJq0ZVoH54VaCdCS9VCA/BrSeVYlcTbzhrxAkEAnB0Ee1AYggvplok65LehzsygMvIrqk2jh5RZ8BqBBU/PguTl6xAtpP4IIv690yLQmav/eAe6iw3dqzgJuvy4AQJALVtGwDobEkKuzHTqSbQWFNdVRa4KdKcR32Opv/NzaTXEreNdppfViNeFJeLVXTwMIdBtUBI2urwifvRnQxERgQJACTRMx1h5mXFKXG3K4yFQkPjPmTFeEbRyJ9Q/WUC2yrFh8r28MJfLmSbBJA8Q8hnjYqwZa+JIxVoT77xmFnhAAQJBAI7/R9h+6xRsddDe9xClY3K/DCDd/g8d5WIxgUCb1AKGmGAj9wzt5DxGm5J2bX+HG1yntCN7pNe3z2se+1zXBD8=';

// Types
export interface Database {
  id: number;
  name: string;
  source: string;
  email?: string;
  phone?: string;
  address?: string;
  financialNumber?: string;
  logo?: string;
  vatNumber?: string;
  fax?: string;
  capital?: number;
  registrationNumber?: string;
  type?: number;
  region?: string;
  databaseBanksInfo?: any[];
}

export interface Role {
  name: string;
  forms: any[];
  id: number;
  isDeleted: boolean;
  createdUser: string;
  createdDate: string;
}

export interface LoginRSAResponse {
  success: boolean;
  message: string;
  errorMessage: string;
  errorCode: string | null;
  dataCount: number;
  data: {
    id: number;
    name: string;
    username: string;
    password: string;
    confirmPassword: string | null;
    email: string;
    description: string;
    extension: string;
    phone: string;
    userDatabases: any;
    userRoles: any;
    modifyHisOperationsOnly: boolean;
    isLogged: boolean;
    hostName: string;
    loggedDatabase: string;
    actionDate: string;
    roles: Role[];
    databases: Database[];
    lockBefore: number;
    hideTransactionsBefore: number;
  };
}

export interface LoginResult {
  success: boolean;
  isLogged: boolean;
  dbList: Database[];
}

export interface SignInResult {
  isOk: boolean;
  data?: any;
  message?: string;
}

export interface LogoutResult {
  success: boolean;
  message?: string;
  errorMessage?: string;
}

// Global app user state (you might want to use a proper state management solution)
export const appUser = {
  UserId: 0,
  userId: 0, // Alias for UserId for API compatibility
  Pass: '',
  UserName: '',
  Token: '',
  DbName: '',
  apiURL: 'http://*************:8055',
  identityURL: 'http://*************:2828', // Identity server URL
  identityToken: '',
  clientKey: IdentityKey,
  applicationId: ApplicationId
};

// JWT decode function (simple implementation)
function decodeJWT(token: string): any {
  try {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(atob(base64).split('').map(function (c) {
      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
    }).join(''));
    return JSON.parse(jsonPayload);
  } catch (error) {
    console.error('Error decoding JWT:', error);
    return {};
  }
}

// Step 1: Login with RSA encryption
export async function login(username: string, password: string): Promise<LoginResult> {
  let encryptedPassWord = '';
  const encrypt = new JSEncrypt();
  encrypt.setPublicKey(rsaPubKey);
  encryptedPassWord = encrypt.encrypt(password) || '';

  try {
    const response = await fetch(`${IdentityUrl}/api/Login/LoginRSA?ApplicationId=${ApplicationId}`, {
      method: "POST",
      headers: {
        "Accept": "*/*",
        "ClientKey": IdentityKey,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        'username': username,
        'password': encryptedPassWord
      }),
    });

    const result: LoginRSAResponse = await response.json();
    if (response.ok && result.success) {
      if (!result.data.isLogged) {
        const databases = result.data.databases.map((db) => ({
          id: db.id,
          name: db.name,
          source: db.source,
        }));
        return { success: result.success, isLogged: result.data.isLogged, dbList: databases };
      }
      else {
        return { success: result.success, isLogged: result.data.isLogged, dbList: [] };
      }
    } else {
      return { success: result.success, isLogged: false, dbList: [] };
    }
  } catch (error) {
    console.error("Login failed:", error);
    return { success: false, isLogged: false, dbList: [] };
  }
}

// Step 2: Generate token with selected database
export async function signIn(username: string, password: string): Promise<SignInResult> {
  try {
    console.log(username, password, appUser.DbName);

    let encryptedPassWord = '';
    const encrypt = new JSEncrypt();
    encrypt.setPublicKey(rsaPubKey);
    encryptedPassWord = encrypt.encrypt(password) || '';

    const response = await fetch(`${IdentityUrl}/api/Login/GenerateTokenRSA?DatabaseName=${appUser.DbName}&ApplicationId=${ApplicationId}`, {
      method: 'POST',
      headers: {
        'accept': '*/*',
        'ClientKey': IdentityKey,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        'username': username,
        'password': encryptedPassWord
      }),
    });

    const result = await response.json();
    if (response.ok && result.success) {
      if (result.data.length > 0) {
        const decodedAccessToken = decodeJWT(result.data);
        appUser.UserId = parseInt(decodedAccessToken.userid);
        appUser.userId = parseInt(decodedAccessToken.userid); // Set alias for API compatibility
        appUser.Pass = password;
        appUser.UserName = username;
        appUser.Token = result.data;
        appUser.identityToken = result.data;

        // Save session for persistent login
        try {
          await saveUserSession(
            appUser.UserId,
            appUser.UserName,
            appUser.Token,
            appUser.identityToken,
            appUser.DbName
          );
          console.log('✅ User session saved for persistent login');
        } catch (sessionError) {
          console.warn('⚠️ Failed to save session, but login successful:', sessionError);
        }

        return {
          isOk: true,
          data: appUser
        };
      } else {
        return {
          isOk: false,
          data: undefined
        };
      }
    } else {
      return {
        isOk: false,
        data: undefined
      };
    }
  } catch (error) {
    console.error("SignIn failed:", error);
    return {
      isOk: false,
      message: "Authentication failed"
    };
  }
}

// Logout function
export async function logout(): Promise<LogoutResult> {
  try {
    const response = await fetch(
      `${appUser.identityURL}/api/Role/Logout?UserId=${appUser.userId}&ApplicationId=${appUser.applicationId}`,
      {
        method: 'PUT',
        headers: {
          'accept': '*/*',
          'ClientKey': appUser.clientKey,
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ' + appUser.identityToken,
        },
      }
    );

    const result = await response.json();

    if (response.ok) {
      // Clear user data on successful logout
      appUser.UserId = 0;
      appUser.userId = 0;
      appUser.Pass = '';
      appUser.UserName = '';
      appUser.Token = '';
      appUser.DbName = '';
      appUser.identityToken = '';

      // Clear persistent session
      try {
        await clearUserSession();
        console.log('✅ User session cleared');
      } catch (sessionError) {
        console.warn('⚠️ Failed to clear session, but logout successful:', sessionError);
      }

      return {
        success: true,
        message: result.message || 'Logged out successfully'
      };
    } else {
      return {
        success: false,
        message: result.message || 'Logout failed',
        errorMessage: result.errorMessage
      };
    }
  } catch (error) {
    console.error('Logout error:', error);
    return {
      success: false,
      message: 'An error occurred during logout',
      errorMessage: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}
