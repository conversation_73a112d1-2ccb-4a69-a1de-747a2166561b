
import { dbService } from './indexedDBService';
import { Invoice, OrderItem, CustomerInfo } from '../types';

export interface EnrichedInvoice extends Invoice {
  items: OrderItem[];
  customer: CustomerInfo;
}

export const getInvoices = async (): Promise<EnrichedInvoice[]> => {
  await dbService.init();
  const invoices = await dbService.getAll<Invoice>('invoices');
  const enrichedInvoices: EnrichedInvoice[] = [];

  for (const invoice of invoices) {
    if (invoice) { // Add this check
      // const order = await dbService.get<any>('orders', invoice.orderId);
      // if (order) {
        enrichedInvoices.push({
          ...invoice,
          items: invoice.items,
          customer: invoice.customerInfo,
        });
      // }
    } // Add this closing bracket
  }

  return enrichedInvoices;
};
