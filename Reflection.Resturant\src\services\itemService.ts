import { appUser } from './authService';
import { Product, Category, Family, ItemUnit, UnitPrice, Currency, Option, InvoiceDto, InvoiceDetailDto, InvoiceVoucher, Voucher, VoucherDetail } from '../types';
import { dbService } from './indexedDBService';

// API Response interfaces
export interface ItemListResponse {
  success: boolean;
  methodName: string;
  errorMessage: string;
  data: Product[];
}

export interface FamilyListResponse {
  success: boolean;
  methodName: string;
  errorMessage: string;
  data: Family[];
}

export interface ItemUnitListResponse {
  success: boolean;
  methodName: string;
  errorMessage: string;
  data: ItemUnit[];
}

export interface UnitPriceListResponse {
  success: boolean;
  methodName: string;
  errorMessage: string;
  data: UnitPrice[];
}

export interface CurrencyListResponse {
  success: boolean;
  methodName: string;
  errorMessage: string;
  data: Currency[];
}

export interface OptionListResponse {
  success: boolean;
  methodName: string;
  errorMessage: string;
  data: Option[];
}

export interface EnrichedProduct extends Product {
  units: ItemUnit[];
  prices: UnitPrice[];
  defaultPrice?: number;
  defaultCurrency?: Currency;
}

// Initialize IndexedDB
export async function initializeDB(): Promise<void> {
  await dbService.init();
}
// Post Data to server

async function postData(endpoint: string, data: any): Promise<any> {
  const response = await fetch(
    `${appUser.apiURL}${endpoint}`,
    {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${appUser.identityToken}`,
        'ClientKey': appUser.clientKey,
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    }
  );

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  const result = await response.json();

  if (!result.success) {
    throw new Error(result.errorMessage || 'API call failed');
  }

  return result;
}

//
// Generic API call function
async function makeAPICall<T>(endpoint: string, stmp: number = 0): Promise<T> {
  const response = await fetch(
    `${appUser.apiURL}${endpoint}?stmp=${stmp}`,
    {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${appUser.identityToken}`,
        'ClientKey': appUser.clientKey,
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }
    }
  );

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  const result = await response.json();

  if (!result.success) {
    throw new Error(result.errorMessage || 'API call failed');
  }

  return result;
}

// Get item list from API
export async function getItemList(stmp: number = 0): Promise<Product[]> {
  try {
    const result: ItemListResponse = await makeAPICall('/api/Item/GetItemListAsync', stmp);

    // Transform API data to include UI-compatible properties
    return result.data.map(item => ({
      ...item,
      price: 0, // Will be populated from UnitPrice data
      category: item.familyId.toString(), // Map familyId to category for compatibility
      hasModifiers: false // You might want to determine this based on item properties
    }));
  } catch (error) {
    console.error('Error fetching items:', error);
    throw error;
  }
}

// Get family list from API
export async function getFamilyList(stmp: number = 0): Promise<Family[]> {
  try {
    const result: FamilyListResponse = await makeAPICall('/api/Family/GetFamilyListAsync', stmp);

    // Filter out deleted families
    return result.data.filter(family => !family.isDeleted);
  } catch (error) {
    console.error('Error fetching families:', error);
    throw error;
  }
}

// Get item unit list from API
export async function getItemUnitList(stmp: number = 0): Promise<ItemUnit[]> {
  try {
    const result: ItemUnitListResponse = await makeAPICall('/api/ItemUnit/GetItemUnitListAsync', stmp);

    // Filter out deleted units
    return result.data.filter(unit => !unit.isDeleted);
  } catch (error) {
    console.error('Error fetching item units:', error);
    throw error;
  }
}

// Get unit price list from API
export async function getUnitPriceList(stmp: number = 0): Promise<UnitPrice[]> {
  try {
    const result: UnitPriceListResponse = await makeAPICall('/api/UnitPriceList/GetUnitPriceListAsync', stmp);

    return result.data;
  } catch (error) {
    console.error('Error fetching unit prices:', error);
    throw error;
  }
}

// Get currency list from API
export async function getCurrencyList(stmp: number = 0): Promise<Currency[]> {
  try {
    const result: CurrencyListResponse = await makeAPICall('/api/currency/GetCurrencyListAsync', stmp);

    // Filter out deleted currencies
    return result.data.filter(currency => !currency.isDeleted);
  } catch (error) {
    console.error('Error fetching currencies:', error);
    throw error;
  }
}

// Get option list from API
export async function getOptionList(): Promise<Option[]> {
  try {
    const result: OptionListResponse = await makeAPICall('/api/Option/GetOptionListAsync');

    return result.data;
  } catch (error) {
    console.error('Error fetching options:', error);
    throw error;
  }
}

// Function to convert POS invoice to server InvoiceDto format
function convertPosInvoiceToDto(posInvoice: any): InvoiceDto {
  // Map POS invoice items to InvoiceDetailDto
  const invoiceDetails: InvoiceDetailDto[] = posInvoice.items.map((item: any, index: number) => ({
    id: item.id, // Will be set by server
    itemUnitId: item.units[0].id || 0, // Adjust based on your item structure
    quantity: item.quantity || 1,
    price: item.price || 0,
    discount: item.discount || 0,
    netPrice: (item.price || 0) * (item.quantity || 1) - (item.discount || 0),
    vat: item.vat || 0,
    cosmeticTax: item.cosmeticTax || 0,
    firstTax: item.firstTax || 0,
    secondTax: item.secondTax || 0,
    notes: item.notes || ''
  }));

  let x: Date = new Date(posInvoice.date);
  let hoursDiff: number = x.getHours() - x.getTimezoneOffset() / 60;
  let minutesDiff: number = (x.getHours() - x.getTimezoneOffset()) % 60;
  x.setHours(hoursDiff);
  x.setMinutes(minutesDiff);
  // Create main invoice DTO
  const invoiceDto: InvoiceDto = {
    invoiceType: 77, // Adjust based on your business logic (e.g., 1 for sales invoice)
    reference: posInvoice.invoiceNumber || '',
    auxiliaryId: 0, // Set based on your business logic
    currencyId: 1, // Set based on your default currency
    exchangeRate: 1, // Default exchange rate
    exchangeRate2: 90000, // Default secondary exchange rate
    date: x,
    discount: posInvoice.discount || 0,
    valueDiscount: posInvoice.discountType === 'percentage' ?
      (posInvoice.subtotal * (posInvoice.discount / 100)) :
      posInvoice.discount,
    notes: `POS Order: ${posInvoice.orderNumber || ''} | Payment: ${posInvoice.paymentMethod || ''} | Order Type: ${posInvoice.orderType || ''}`,
    total: posInvoice.total || 0,
    contactId: posInvoice.customerInfo?.id || null, // Adjust based on your customer structure
    invoiceDetails: invoiceDetails
  };

  return invoiceDto;
}

async function convertReceiptsToVoucherDto(invoiceId: string): Promise<Voucher> {
  const rec = await dbService.getVoucherByInvoiceReference(invoiceId);
  const detailsArray = Array.isArray(rec.paymentDetails)
    ? rec.paymentDetails
    : rec.paymentDetails
      ? [rec.paymentDetails]
      : [];
  const vDetails: VoucherDetail[] = detailsArray.map((item: any, index: number) => ({
    id: 1,
    currencyId: 1,
    paymentType: 1,
    amount: item.total,
    amount1: item.total,
    amount2: item.total / 90000,
    exchangeRate: 1,
    exchangeRate2: 90000,
    description: '',
    checkNumber: '',
    bank: '',
    orderOf: '',
    dueDate: new Date(),
    notes: ''
  }));

  const receipts: Voucher = {
    id: 0,
    clientId: 0,
    clientCurrencyId: 1,
    branchId: 1,
    date: new Date(rec.date),
    reference: rec.receiptNumber,
    notes: '',
    createdUser: 0,
    invoiceReference: rec.invoiceId,
    posted: 0,
    voucherDetails: vDetails
  };

  return receipts;
}

// Main function to sync unposted invoices to server
export async function syncUnpostedInvoicesToServer(): Promise<{
  success: number;
  failed: number;
  errors: Array<{ invoiceId: string; error: string }>;
}> {
  const results = {
    success: 0,
    failed: 0,
    errors: [] as Array<{ invoiceId: string; error: string }>
  };

  try {
    // Get all unposted invoices
    const unpostedInvoices = await dbService.getUnpostedInvoices();

    if (unpostedInvoices.length === 0) {
      console.log('No unposted invoices found');
      return results;
    }

    console.log(`Found ${unpostedInvoices.length} unposted invoices to sync`);

    // Process each invoice
    for (const posInvoice of unpostedInvoices) {
      try {
        // Convert POS invoice to server format
        const invoiceDto = convertPosInvoiceToDto(posInvoice);
        const voucherDto = await convertReceiptsToVoucherDto(posInvoice.id)

        const invoiceVoucher = {
          invoice: invoiceDto,
          voucher: voucherDto
        };

        // Post to server
        const response = await postData('/api/invoice/SavePOSInvoiceWithReceipt', invoiceVoucher); // Adjust endpoint as needed

        if (response.success) {
          // Update invoice as posted in IndexedDB
          await dbService.updateInvoicePostedStatus(posInvoice.id, true);
          results.success++;
          console.log(`Successfully synced invoice: ${posInvoice.invoiceNumber}`);
        } else {
          results.failed++;
          results.errors.push({
            invoiceId: posInvoice.id,
            error: response.message || 'Unknown server error'
          });
          console.error(`Failed to sync invoice ${posInvoice.invoiceNumber}:`, response.message);
        }
      } catch (error) {
        results.failed++;
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        results.errors.push({
          invoiceId: posInvoice.id,
          error: errorMessage
        });
        console.error(`Error syncing invoice ${posInvoice.invoiceNumber}:`, error);
      }
    }

    console.log(`Sync completed: ${results.success} successful, ${results.failed} failed`);
    return results;

  } catch (error) {
    console.error('Error during invoice sync:', error);
    throw error;
  }
}

// Sync data with IndexedDB using stamp-based incremental updates
export async function syncData(forceRefresh: boolean = false): Promise<{
  items: Product[];
  families: Family[];
  itemUnits: ItemUnit[];
  unitPrices: UnitPrice[];
  currencies: Currency[];
  options: Option[];
}> {
  await initializeDB();

  try {
    let itemsMaxStmp = 0;
    let familiesMaxStmp = 0;
    let unitsMaxStmp = 0;
    let pricesMaxStmp = 0;
    let currenciesMaxStmp = 0;

    if (!forceRefresh) {
      // Get max stamps from local data for incremental updates
      [itemsMaxStmp, familiesMaxStmp, unitsMaxStmp, pricesMaxStmp, currenciesMaxStmp] = await Promise.all([
        dbService.getMaxStmp('items'),
        dbService.getMaxStmp('families'),
        dbService.getMaxStmp('itemUnits'),
        dbService.getMaxStmp('unitPrices'),
        dbService.getMaxStmp('currencies')
      ]);
    }

    // Fetch updates from API based on stamps (0 for force refresh)
    const [
      itemsUpdate,
      familiesUpdate,
      unitsUpdate,
      pricesUpdate,
      currenciesUpdate,
      optionsUpdate
    ] = await Promise.all([
      getItemList(itemsMaxStmp),
      getFamilyList(familiesMaxStmp),
      getItemUnitList(unitsMaxStmp),
      getUnitPriceList(pricesMaxStmp),
      getCurrencyList(currenciesMaxStmp),
      getOptionList() // Options don't have stamps, always fetch all
    ]);

    // Update local storage with new/updated data
    if (itemsUpdate.length > 0) {
      await dbService.bulkPut('items', itemsUpdate);
    }
    if (familiesUpdate.length > 0) {
      await dbService.bulkPut('families', familiesUpdate);
    }
    if (unitsUpdate.length > 0) {
      await dbService.bulkPut('itemUnits', unitsUpdate);
    }
    if (pricesUpdate.length > 0) {
      await dbService.bulkPut('unitPrices', pricesUpdate);
    }
    if (currenciesUpdate.length > 0) {
      await dbService.bulkPut('currencies', currenciesUpdate);
    }
    if (optionsUpdate.length > 0) {
      await dbService.bulkPut('options', optionsUpdate);
    }

    // Get all active data from local storage (filter by isDeleted where applicable)
    const [allItems, allFamilies, allUnits, allPrices, allCurrencies, allOptions] = await Promise.all([
      dbService.getAll<Product>('items'),
      dbService.getAll<Family>('families'),
      dbService.getAll<ItemUnit>('itemUnits'),
      dbService.getAll<UnitPrice>('unitPrices'),
      dbService.getAll<Currency>('currencies'),
      dbService.getAll<Option>('options')
    ]);

    // Filter out deleted items where applicable
    const items = allItems.filter(item => !item.isDeleted);
    const families = allFamilies.filter(family => !family.isDeleted);
    const itemUnits = allUnits.filter(unit => !unit.isDeleted);
    const unitPrices = allPrices; // UnitPrices don't have isDeleted field
    const currencies = allCurrencies.filter(currency => !currency.isDeleted);
    const options = allOptions; // Options don't have isDeleted field

    // Update metadata
    const newMaxStmp = Math.max(
      items.length > 0 ? Math.max(...items.map(i => i.stmp)) : 0,
      families.length > 0 ? Math.max(...families.map(f => f.stmp)) : 0,
      itemUnits.length > 0 ? Math.max(...itemUnits.map(u => u.stmp)) : 0,
      unitPrices.length > 0 ? Math.max(...unitPrices.map(p => p.stmp)) : 0,
      currencies.length > 0 ? Math.max(...currencies.map(c => c.stmp)) : 0
    );

    await dbService.put('metadata', {
      key: 'lastSync',
      lastSync: Date.now(),
      maxStmp: newMaxStmp
    });

    return { items, families, itemUnits, unitPrices, currencies, options };

  } catch (error) {
    console.error('Error syncing data:', error);

    // Fallback to local data if available
    try {
      const [allItems, allFamilies, allUnits, allPrices, allCurrencies, allOptions] = await Promise.all([
        dbService.getAll<Product>('items'),
        dbService.getAll<Family>('families'),
        dbService.getAll<ItemUnit>('itemUnits'),
        dbService.getAll<UnitPrice>('unitPrices'),
        dbService.getAll<Currency>('currencies'),
        dbService.getAll<Option>('options')
      ]);

      // Filter out deleted items
      const items = allItems.filter(item => !item.isDeleted);
      const families = allFamilies.filter(family => !family.isDeleted);
      const itemUnits = allUnits.filter(unit => !unit.isDeleted);
      const unitPrices = allPrices;
      const currencies = allCurrencies.filter(currency => !currency.isDeleted);
      const options = allOptions;

      return { items, families, itemUnits, unitPrices, currencies, options };
    } catch (localError) {
      console.error('Error loading local data:', localError);
      throw error; // Re-throw original error if local data also fails
    }
  }
}

// Enrich products with units, prices, and family information
export async function getEnrichedProducts(priceListId: number = 1): Promise<{
  products: EnrichedProduct[];
  families: Family[];
  categories: Category[];
  currencies: Currency[];
  options: Option[];
}> {
  try {
    const { items, families, itemUnits, unitPrices, currencies, options } = await syncData();

    // Create lookup maps for efficient data joining
    const familyMap = new Map(families.map(family => [family.id, family]));
    const unitsMap = new Map<number, ItemUnit[]>();
    const pricesMap = new Map<number, UnitPrice[]>();

    // Group units by itemId
    itemUnits.forEach(unit => {
      if (!unitsMap.has(unit.itemId)) {
        unitsMap.set(unit.itemId, []);
      }
      unitsMap.get(unit.itemId)!.push(unit);
    });

    // Group prices by itemUnitId
    unitPrices.forEach(price => {
      if (!pricesMap.has(price.itemUnitId)) {
        pricesMap.set(price.itemUnitId, []);
      }
      pricesMap.get(price.itemUnitId)!.push(price);
    });

    // Get default currency (first one or USD if available)
    const defaultCurrency = currencies.find(c => c.id == 1) || currencies[0];

    if (options.find(x => x.id == 116 && x.value != '')) {
      priceListId = parseInt(options.find(x => x.id == 116)!.value);
    }

    // Enrich products with all related data
    const enrichedProducts: EnrichedProduct[] = items.map(product => {
      const productUnits = unitsMap.get(product.id) || [];
      const defaultUnit = productUnits.find(u => u.isDefaultUnit) || productUnits[0];

      let productPrices: UnitPrice[] = [];
      let defaultPrice = 0;

      if (defaultUnit) {
        productPrices = pricesMap.get(defaultUnit.id) || [];
        const priceForList = productPrices.find(p => p.priceListId === priceListId);
        defaultPrice = priceForList ? priceForList.price : 0;
      }

      return {
        ...product,
        familyName: familyMap.get(product.familyId)?.name || 'Unknown Family',
        units: productUnits,
        prices: productPrices,
        defaultPrice,
        defaultCurrency,
        price: defaultPrice, // Update the price property
        category: product.familyId.toString(),
        hasModifiers: productUnits.length > 1 // Has modifiers if multiple units
      };
    });

    // Create categories from families
    const categories: Category[] = [
      { id: 'all', name: 'All' },
      ...families.map(family => ({
        id: family.id.toString(),
        name: family.name
      }))
    ];

    return {
      products: enrichedProducts,
      families,
      categories,
      currencies,
      options
    };
  } catch (error) {
    console.error('Error fetching enriched products:', error);
    throw error;
  }
}

// Get products with family information (backward compatibility)
export async function getProductsWithFamilies(): Promise<{ products: Product[], families: Family[], categories: Category[] }> {
  const { products, families, categories } = await getEnrichedProducts();
  return { products, families, categories };
}

// Get categories based on families from API
export async function getCategories(): Promise<Category[]> {
  try {
    const families = await getFamilyList();

    const categories: Category[] = [
      { id: 'all', name: 'All' },
      ...families.map(family => ({
        id: family.id.toString(),
        name: family.name
      }))
    ];

    return categories;
  } catch (error) {
    console.error('Error fetching categories:', error);
    // Return default categories on error
    return [
      { id: 'all', name: 'All' },
      { id: '1', name: 'General' }
    ];
  }
}

// Filter products by category (familyId) - works with both Product and EnrichedProduct
export function filterProductsByCategory<T extends Product>(products: T[], categoryId: string): T[] {
  if (categoryId === 'all') {
    return products.filter(p => !p.isDeleted && !p.deactivated);
  }

  return products.filter(product => !product.isDeleted && !product.deactivated && product.familyId.toString() === categoryId);
}

// Search products by name - works with both Product and EnrichedProduct
export function searchProducts<T extends Product>(products: T[], query: string): T[] {
  if (!query.trim()) {
    return products;
  }

  const lowercaseQuery = query.toLowerCase();
  return products.filter(product => !product.isDeleted && !product.deactivated &&
    (product.name.toLowerCase().includes(lowercaseQuery) ||
      product.abbreviation.toLowerCase().includes(lowercaseQuery))
  );
}

// Check if user is authenticated for API calls
export function isAuthenticated(): boolean {
  return !!(appUser.identityToken && appUser.clientKey);
}

// Format price with currency
export function formatPrice(price: number, currency?: Currency): string {
  if (!currency) {
    return price.toFixed(2);
  }

  // Handle different currency formatting
  const formattedPrice = price.toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });

  return `${formattedPrice} ${currency.symbol}`;
}

// Get product price for specific unit and price list
export function getProductPrice(product: EnrichedProduct, unitId?: number, priceListId: number = 1): number {
  if (!product.units.length) return product.defaultPrice || 0;

  const targetUnit = unitId
    ? product.units.find(u => u.id === unitId)
    : product.units.find(u => u.isDefaultUnit) || product.units[0];

  if (!targetUnit) return product.defaultPrice || 0;

  const unitPrice = product.prices.find(p => p.itemUnitId === targetUnit.id && p.priceListId === priceListId);
  return unitPrice ? unitPrice.price : 0;
}

// Get available units for a product
export function getProductUnits(product: EnrichedProduct): ItemUnit[] {
  return product.units.filter(unit => !unit.isDeleted);
}

// Get default unit for a product
export function getDefaultUnit(product: EnrichedProduct): ItemUnit | undefined {
  return product.units.find(unit => unit.isDefaultUnit) || product.units[0];
}

// Force refresh all data
export async function refreshAllData(): Promise<void> {
  await syncData(true);
  await syncUnpostedInvoicesToServer();
}

// Get option value by description
export async function getOptionValue(description: string): Promise<string | null> {
  await initializeDB();
  const options = await dbService.getAll<Option>('options');
  const option = options.find(opt => opt.description === description);
  return option ? option.value : null;
}

// Get default currency ID from options
export async function getDefaultCurrencyId(): Promise<number> {
  const defaultCurrencyValue = await getOptionValue('Default Currency');
  return defaultCurrencyValue ? parseInt(defaultCurrencyValue) : 1;
}

// Get last sync time
export async function getLastSyncTime(): Promise<number> {
  await initializeDB();
  const metadata = await dbService.get<{ lastSync: number; maxStmp: number }>('metadata', 'lastSync');
  return metadata?.lastSync || 0;
}

// Get default currency for the application
export async function getDefaultCurrency(): Promise<Currency | null> {
  await initializeDB();
  const currencies = await dbService.getAll<Currency>('currencies');

  // Try to get default currency from options
  const defaultCurrencyId = await getDefaultCurrencyId();
  const defaultCurrency = currencies.find(c => c.id === defaultCurrencyId);

  if (defaultCurrency) {
    return defaultCurrency;
  }

  // Fallback to first available currency
  return currencies.length > 0 ? currencies[0] : null;
}

// Get item image as data URL (since API returns base64)
export function getItemImageUrl(item: Product): string {
  if (item.image && item.image !== '') {
    // Check if it's already a data URL
    if (item.image.startsWith('data:')) {
      return item.image;
    }
    // Assume it's base64 and create data URL
    return `data:image/png;base64,${item.image}`;
  }

  // Return placeholder image URL
  return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtc2l6ZT0iMTgiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIiBmaWxsPSIjOTk5Ij5ObyBJbWFnZTwvdGV4dD48L3N2Zz4=';
}
