import { dbService } from './indexedDBService';
import { appUser } from './authService';
import { Order, Invoice, Receipt, OrderItem, CustomerInfo, OrderType } from '../types';
import { linkOrderToTable, clearTable, parseDiningTableId } from './tableService';

// Generate unique IDs
function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// Generate order number (format: ORD-YYYYMMDD-XXXX)
function generateOrderNumber(): string {
  const date = new Date();
  const dateStr = date.getFullYear().toString() +
    (date.getMonth() + 1).toString().padStart(2, '0') +
    date.getDate().toString().padStart(2, '0');
  const sequence = Math.floor(Math.random() * 9999).toString().padStart(4, '0');
  return `ORD-${dateStr}-${sequence}`;
}

// Generate invoice number (format: INV-YYYYMMDD-XXXX)
function generateInvoiceNumber(): string {
  const date = new Date();
  const dateStr = date.getFullYear().toString() +
    (date.getMonth() + 1).toString().padStart(2, '0') +
    date.getDate().toString().padStart(2, '0');
  const sequence = Math.floor(Math.random() * 9999).toString().padStart(4, '0');
  return `INV-${dateStr}-${sequence}`;
}

// Generate receipt number (format: RCP-YYYYMMDD-XXXX)
function generateReceiptNumber(): string {
  const date = new Date();
  const dateStr = date.getFullYear().toString() +
    (date.getMonth() + 1).toString().padStart(2, '0') +
    date.getDate().toString().padStart(2, '0');
  const sequence = Math.floor(Math.random() * 9999).toString().padStart(4, '0');
  return `RCP-${dateStr}-${sequence}`;
}

// Calculate order totals
export function calculateOrderTotals(
  items: OrderItem[],
  discount: number = 0,
  discountType: 'percentage' | 'fixed' = 'percentage',
  taxRate: number = 0
) {
  const subtotal = items.reduce((total, item) => {
    const modifierPrice = item.modifierPrice || 0;
    return total + (item.price || 0 + modifierPrice) * item.quantity * (1 - (item.discount || 0) / 100);
  }, 0);

  const discountAmount = discountType === 'percentage'
    ? subtotal * (discount / 100)
    : Math.min(discount, subtotal);

  const taxableAmount = subtotal - discountAmount;
  const tax = items.reduce((tax, item) => {
    const modifierPrice = item.modifierPrice || 0;
    return tax + (((item.price || 0) + modifierPrice) * item.quantity * (1 - (item.discount || 0) / 100) * (1 - 1 / (1 + item.vat / 100)));
  }, 0);
  //taxableAmount * taxRate;
  const total = taxableAmount; // + tax;

  return {
    subtotal,
    discount: discountAmount,
    taxableAmount,
    tax,
    total
  };
}

// Save order without completing (manual save)
export async function saveOrder(
  items: OrderItem[],
  customerInfo: CustomerInfo,
  orderType: OrderType,
  discount: number = 0,
  discountType: 'percentage' | 'fixed' = 'percentage',
  tableId?: string // For dine-in orders
): Promise<Order> {
  if (!items || items.length === 0) {
    throw new Error('Cannot save empty order');
  }

  await dbService.init();

  const now = Date.now();
  const date = new Date(now);
  const dateStr = date.toLocaleDateString();
  const timeStr = date.toLocaleTimeString();

  // Calculate totals
  const totals = calculateOrderTotals(items, discount, discountType);

  // Generate IDs and numbers
  const orderId = generateId();
  const orderNumber = generateOrderNumber();

  // Handle table information for dine-in orders
  let originalTableId: number | undefined;
  let tableName: string | undefined;

  if (orderType === 'dine-in' && tableId) {
    const { tableId: parsedTableId } = parseDiningTableId(tableId);
    originalTableId = parsedTableId;
    tableName = `Table ${parsedTableId}`;
  }

  // Create order with pending status
  const order: Order = {
    id: orderId,
    orderNumber,
    invoiceId: '', // Will be set during checkout
    date: dateStr,
    time: timeStr,
    orderType,
    customerInfo,
    items: [...items], // Deep copy
    total: totals.total,
    status: 'pending',
    createdBy: appUser.UserName || 'Unknown',
    createdAt: now,
    updatedAt: now,
    tableId: originalTableId,
    tableName
  };

  try {
    // Save order to IndexedDB
    await dbService.put('orders', order);

    // Link order to table if it's a dine-in order
    if (orderType === 'dine-in' && tableId) {
      await linkOrderToTable(orderId, tableId);
    }

    return order;
  } catch (error) {
    console.error('Error saving order:', error);
    throw new Error('Failed to save order');
  }
}

// Update existing order (for modifying saved orders)
export async function updateOrder(
  orderId: string,
  items: OrderItem[],
  customerInfo: CustomerInfo,
  discount: number = 0,
  discountType: 'percentage' | 'fixed' = 'percentage'
): Promise<Order> {
  if (!items || items.length === 0) {
    throw new Error('Cannot update order with empty items');
  }

  await dbService.init();

  const existingOrder = await dbService.get<Order>('orders', orderId);
  if (!existingOrder) {
    throw new Error('Order not found');
  }

  // Calculate new totals
  const totals = calculateOrderTotals(items, discount, discountType);

  // Update order
  const updatedOrder: Order = {
    ...existingOrder,
    items: [...items], // Deep copy
    customerInfo,
    total: totals.total,
    updatedAt: Date.now()
  };

  try {
    await dbService.put('orders', updatedOrder);
    return updatedOrder;
  } catch (error) {
    console.error('Error updating order:', error);
    throw new Error('Failed to update order');
  }
}

// Checkout function - saves order, invoice, and receipt
export async function checkout(
  items: OrderItem[],
  customerInfo: CustomerInfo,
  orderType: OrderType,
  discount: number = 0,
  discountType: 'percentage' | 'fixed' = 'percentage',
  paymentDetails: any,
  paymentMethod: string,
  tableId?: string, // Now accepts composite ID for dine-in orders
  existingOrderId?: string // Optional: ID of existing order to update
): Promise<{ order: Order; invoice: Invoice; receipt: Receipt }> {

  if (!items || items.length === 0) {
    throw new Error('Cannot checkout with empty order');
  }

  await dbService.init();

  const now = Date.now();
  const date = new Date(now);
  const dateStr = date.toLocaleDateString();
  const timeStr = date.toLocaleTimeString();

  // Calculate totals
  const totals = calculateOrderTotals(items, discount, discountType);

  // Check if we're updating an existing order or creating a new one
  let orderId: string;
  let orderNumber: string;
  let existingOrder: Order | null = null;

  if (existingOrderId) {
    // Update existing order
    const foundOrder = await dbService.get<Order>('orders', existingOrderId);
    if (!foundOrder) {
      throw new Error('Existing order not found');
    }
    existingOrder = foundOrder;
    orderId = existingOrderId;
    orderNumber = existingOrder.orderNumber;
  } else {
    // Create new order
    orderId = generateId();
    orderNumber = generateOrderNumber();
  }

  // Generate IDs for invoice and receipt
  const invoiceId = generateId();
  const receiptId = generateId();
  const invoiceNumber = generateInvoiceNumber();
  const receiptNumber = generateReceiptNumber();

  // Create or update order
  let originalTableId: number | undefined;
  let tableName: string | undefined;

  if (orderType === 'dine-in' && tableId) {
    const { tableId: parsedTableId } = parseDiningTableId(tableId);
    originalTableId = parsedTableId;
    tableName = `Table ${parsedTableId}`;
  }

  const order: Order = existingOrder ? {
    // Update existing order
    ...existingOrder,
    invoiceId,
    customerInfo,
    items: [...items], // Deep copy
    total: totals.total,
    status: 'completed',
    updatedAt: now,
    // Preserve original creation data
  } : {
    // Create new order
    id: orderId,
    orderNumber,
    invoiceId,
    date: dateStr,
    time: timeStr,
    orderType,
    customerInfo,
    items: [...items], // Deep copy
    total: totals.total,
    status: 'completed',
    createdBy: appUser.UserName || 'Unknown',
    createdAt: now,
    updatedAt: now,
    tableId: originalTableId,
    tableName
  };

  // Create invoice
  const invoice: Invoice = {
    id: invoiceId,
    invoiceNumber,
    orderNumber,
    date: dateStr,
    time: timeStr,
    orderType,
    customerInfo,
    items: [...items], // Deep copy
    subtotal: totals.subtotal,
    discount: totals.discount,
    discountType,
    taxableAmount: totals.taxableAmount,
    tax: totals.tax,
    taxRate: 0,
    total: totals.total,
    paymentMethod: paymentMethod,
    paymentStatus: 'paid',
    status: 'pending',
    createdBy: appUser.UserName || 'Unknown',
    createdAt: now,
    updatedAt: now,
    posted: false
  };

  // Create receipt
  const receipt: Receipt = {
    id: receiptId,
    invoiceId,
    receiptNumber,
    date: dateStr,
    time: timeStr,
    customerInfo,
    items: [...items], // Deep copy
    subtotal: totals.subtotal,
    discount: totals.discount,
    discountType,
    tax: totals.tax,
    total: totals.total,
    paymentMethod: paymentMethod,
    paymentDetails : paymentDetails,
    createdAt: now
  };

  try {
    // Save all data to IndexedDB
    await Promise.all([
      dbService.put('orders', order),
      dbService.put('invoices', invoice),
      dbService.put('receipts', receipt)
    ]);

    // Link order to table if it's a dine-in order
    if (orderType === 'dine-in' && tableId) {
      await linkOrderToTable(orderId, tableId);
    }

    return { order, invoice, receipt };
  } catch (error) {
    console.error('Error saving checkout data:', error);
    throw new Error('Failed to save order data');
  }
}

// Get all orders
export async function getAllOrders(): Promise<Order[]> {
  await dbService.init();
  const orders = await dbService.getAll<Order>('orders');
  return orders.sort((a, b) => b.createdAt - a.createdAt); // Sort by newest first
}

// Get orders by status
export async function getOrdersByStatus(status: string): Promise<Order[]> {
  await dbService.init();
  return await dbService.getByIndex<Order>('orders', 'status', status);
}

// Get incomplete orders (pending, preparing, ready)
export async function getIncompleteOrders(): Promise<Order[]> {
  await dbService.init();
  const allOrders = await dbService.getAll<Order>('orders');
  return allOrders
    .filter(order => ['pending', 'preparing', 'ready'].includes(order.status))
    .sort((a, b) => b.updatedAt - a.updatedAt); // Sort by most recently updated first
}

// Get incomplete orders by type
export async function getIncompleteOrdersByType(orderType: OrderType): Promise<Order[]> {
  const incompleteOrders = await getIncompleteOrders();
  return incompleteOrders.filter(order => order.orderType === orderType);
}

// Update order status
export async function updateOrderStatus(orderId: string, status: Order['status']): Promise<void> {
  await dbService.init();

  const order = await dbService.get<Order>('orders', orderId);
  if (!order) {
    throw new Error('Order not found');
  }

  const updatedOrder = {
    ...order,
    status,
    updatedAt: Date.now()
  };

  await dbService.put('orders', updatedOrder);

  // Clear table if order is completed or cancelled and it's a dine-in order
  if ((status === 'completed') && order.tableId) {
    await clearTable(order.tableId.toString());
  }

  // Also update invoice status if it exists
  const invoice = await dbService.get<Invoice>('invoices', order.invoiceId);
  if (invoice) {
    const updatedInvoice = {
      ...invoice,
      status,
      updatedAt: Date.now()
    };
    await dbService.put('invoices', updatedInvoice);
  }
}

// Get invoice by ID
export async function getInvoiceById(invoiceId: string): Promise<Invoice | null> {
  await dbService.init();
  return await dbService.get<Invoice>('invoices', invoiceId) || null;
}

// Get receipt by ID
export async function getReceiptById(receiptId: string): Promise<Receipt | null> {
  await dbService.init();
  return await dbService.get<Receipt>('receipts', receiptId) || null;
}

// Search orders by customer name
export async function searchOrdersByCustomer(customerName: string): Promise<Order[]> {
  await dbService.init();
  const allOrders = await dbService.getAll<Order>('orders');
  return allOrders.filter(order =>
    order.customerInfo.name.toLowerCase().includes(customerName.toLowerCase())
  );
}

// Get order statistics
export async function getOrderStatistics(): Promise<{
  totalOrders: number;
  pendingOrders: number;
  completedOrders: number;
  totalRevenue: number;
  todayOrders: number;
  todayRevenue: number;
}> {
  await dbService.init();
  const orders = await dbService.getAll<Order>('orders');

  const today = new Date();
  const todayStr = today.toLocaleDateString();

  const stats = {
    totalOrders: orders.length,
    pendingOrders: orders.filter(o => o.status === 'pending').length,
    completedOrders: orders.filter(o => o.status === 'completed').length,
    totalRevenue: orders.reduce((sum, o) => sum + o.total, 0),
    todayOrders: orders.filter(o => o.date === todayStr).length,
    todayRevenue: orders.filter(o => o.date === todayStr).reduce((sum, o) => sum + o.total, 0)
  };

  return stats;
}

// Add function to get order by ID
export async function getOrderById(orderId: string): Promise<Order | null> {
  await dbService.init();
  const order = await dbService.get<Order>('orders', orderId);
  return order || null;
}

