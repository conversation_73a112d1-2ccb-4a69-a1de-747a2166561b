import { appUser, decodeJWT } from './authService';
import { loadUserSession } from './persistentAuthService';

// Types for role-based access control
export interface RoleForm {
  id: number;
  formName: string;
  commandId: number;
  commandName: string | null;
}

export interface RoleFormsResponse {
  success: boolean;
  methodName: string;
  errorMessage: string;
  data: RoleForm[];
}

export interface UserPermissions {
  canAccessKitchen: boolean;
  canAccessTableManagement: boolean;
  canAccessPOS: boolean;
  canAccessInvoices: boolean;
  forms: RoleForm[];
}

/**
 * Extract roleId from JWT token
 */
export function getRoleIdFromToken(token?: string): number | null {
  try {
    const tokenToUse = token || appUser.Token || appUser.identityToken;

    if (!tokenToUse) {
      console.warn('No token available to extract roleId');
      return null;
    }

    const decodedToken = decodeJWT(tokenToUse);
    console.log('🔍 Decoded JWT token payload:', decodedToken);

    // Common JWT claim names for role ID
    const roleId = decodedToken.roleId ||
                   decodedToken.roleid ||
                   decodedToken.role_id ||
                   decodedToken.RoleId ||
                   decodedToken.role ||
                   decodedToken.Role ||
                   decodedToken.RoleID ||
                   decodedToken.roleid;

    if (roleId) {
      console.log('✅ Found roleId in token:', roleId);
      return parseInt(roleId.toString());
    }

    console.warn('❌ RoleId not found in token payload. Available keys:', Object.keys(decodedToken));
    return null;
  } catch (error) {
    console.error('Error extracting roleId from token:', error);
    return null;
  }
}

/**
 * Get user's accessible forms from the API
 */
export async function getUserRoleForms(roleId?: number): Promise<RoleForm[]> {
  try {
    const roleIdToUse = roleId || getRoleIdFromToken();

    if (!roleIdToUse) {
      console.error('No roleId available to fetch role forms');
      return [];
    }

    // Ensure we have a valid token
    if (!appUser.identityToken) {
      console.error('No identity token available for API call');
      return [];
    }

    console.log('🔍 Fetching role forms for roleId:', roleIdToUse);

    const response = await fetch(
      `http://85.112.90.211:8055/api/Role/GetRolesFormsCommandNames`,
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${appUser.identityToken}`,
          'ClientKey': appUser.clientKey,
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ roleId: roleIdToUse, applicationId: appUser.applicationId })
      }
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result: RoleFormsResponse = await response.json();

    if (!result.success) {
      throw new Error(result.errorMessage || 'Failed to fetch role forms');
    }

    console.log('✅ Role forms fetched successfully:', result.data);
    return result.data;
  } catch (error) {
    console.error('❌ Error fetching role forms:', error);
    return [];
  }
}

/**
 * Get user permissions based on role forms
 */
export async function getUserPermissions(): Promise<UserPermissions> {
  try {
    const forms = await getUserRoleForms();
    
    const permissions: UserPermissions = {
      canAccessKitchen: false,
      canAccessTableManagement: false,
      canAccessPOS: false,
      canAccessInvoices: true, // Default to true for invoices as it's a basic feature
      forms
    };

    // Check each form to determine permissions
    forms.forEach(form => {
      const formName = form.formName.toLowerCase();
      
      if (formName === 'kitchen') {
        permissions.canAccessKitchen = true;
      } else if (formName === 'tables management' || formName === 'table management') {
        permissions.canAccessTableManagement = true;
      } else if (formName === 'pos') {
        permissions.canAccessPOS = true;
      }
    });

    console.log('✅ User permissions calculated:', permissions);
    return permissions;
  } catch (error) {
    console.error('❌ Error getting user permissions:', error);
    
    // Return default permissions on error (minimal access)
    return {
      canAccessKitchen: false,
      canAccessTableManagement: false,
      canAccessPOS: false,
      canAccessInvoices: true,
      forms: []
    };
  }
}

/**
 * Check if user has access to a specific form
 */
export async function hasAccessToForm(formName: string): Promise<boolean> {
  try {
    const permissions = await getUserPermissions();
    
    const normalizedFormName = formName.toLowerCase();
    
    switch (normalizedFormName) {
      case 'kitchen':
        return permissions.canAccessKitchen;
      case 'table management':
      case 'tables management':
        return permissions.canAccessTableManagement;
      case 'pos':
        return permissions.canAccessPOS;
      case 'invoices':
        return permissions.canAccessInvoices;
      default:
        return false;
    }
  } catch (error) {
    console.error('❌ Error checking form access:', error);
    return false;
  }
}

/**
 * Get roleId from current session
 */
export async function getRoleIdFromSession(): Promise<number | null> {
  try {
    const session = await loadUserSession();
    
    if (!session || !session.token) {
      return null;
    }

    return getRoleIdFromToken(session.token);
  } catch (error) {
    console.error('❌ Error getting roleId from session:', error);
    return null;
  }
}
